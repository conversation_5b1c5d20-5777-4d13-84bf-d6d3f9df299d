"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/components/DateRange.tsx":
/*!**************************************!*\
  !*** ./src/components/DateRange.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui_time_picker__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/time-picker */ \"(app-pages-browser)/./src/components/ui/time-picker.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/isBefore.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/isAfter.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/set.mjs\");\n/* harmony import */ var _ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _ui_separator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst DatePicker = (param)=>{\n    let { onChange, className, placeholder = \"Select date\", mode = \"range\", type = \"date\", disabled = false, value, dateFormat = \"dd LLLL, y\", validation, numberOfMonths = mode === \"range\" ? 2 : 1, closeOnSelect = true, showWeekNumbers = false, includeTime = false, timeMode = \"single\", timeFormat = \"HH:mm\", timeInterval = 30, label, labelPosition = \"top\", clearable = false, icon, confirmSelection = true, confirmButtonText = \"Confirm\", modal = false, wrapperClassName = \"\", ...buttonProps } = param;\n    _s();\n    const [dateValue, setDateValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value || (mode === \"range\" ? {\n        from: undefined,\n        to: undefined\n    } : undefined));\n    // We'll use buttonProps directly but ensure we don't pass our custom type prop to the Button component\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [time, setTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [toTime, setToTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State to track pending selection when confirmation button is enabled\n    const [pendingSelection, setPendingSelection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value || undefined);\n    // Set placeholder based on mode\n    const actualPlaceholder = mode === \"range\" ? \"Select date range\" : \"Select date\";\n    // Optimized deep equality check for dates\n    const isDateEqual = (a, b)=>{\n        if (a === b) return true;\n        if (!a || !b) return false;\n        return a instanceof Date && b instanceof Date && a.getTime() === b.getTime();\n    };\n    const isValueEqual = (a, b)=>{\n        if (a === b) return true;\n        if (a instanceof Date && b instanceof Date) return isDateEqual(a, b);\n        if (a && b && typeof a === \"object\" && typeof b === \"object\" && \"from\" in a && \"to\" in a && \"from\" in b && \"to\" in b) {\n            return isDateEqual(a.from, b.from) && isDateEqual(a.to, b.to);\n        }\n        return false;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only update if the value has actually changed\n        if (!isEqual(value, dateValue)) {\n            if (value) {\n                setDateValue(value);\n                // Also update pendingSelection with the value\n                setPendingSelection(value);\n                if (value instanceof Date) {\n                    const hours = value.getHours();\n                    const minutes = value.getMinutes();\n                    if (hours || minutes) setTime({\n                        hour: hours,\n                        minute: minutes\n                    });\n                } else if (\"from\" in value && value.from instanceof Date) {\n                    const { from, to } = value;\n                    const fromHours = from.getHours();\n                    const fromMinutes = from.getMinutes();\n                    if (fromHours || fromMinutes) setTime({\n                        hour: fromHours,\n                        minute: fromMinutes\n                    });\n                    if (to instanceof Date) {\n                        const toHours = to.getHours();\n                        const toMinutes = to.getMinutes();\n                        if (toHours || toMinutes) setToTime({\n                            hour: toHours,\n                            minute: toMinutes\n                        });\n                    }\n                }\n            } else {\n                // When value is null/undefined, reset to initial state but maintain format\n                setDateValue(mode === \"range\" ? {\n                    from: undefined,\n                    to: undefined\n                } : undefined);\n                // Also reset pendingSelection\n                setPendingSelection(undefined);\n            }\n        }\n    }, [\n        value,\n        mode\n    ]);\n    const validateDate = (date)=>{\n        if (!validation) return true;\n        const { minDate, maxDate, disabledDates, disabledDaysOfWeek } = validation;\n        if (minDate && (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_9__.isBefore)(date, minDate)) return false;\n        if (maxDate && (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_10__.isAfter)(date, maxDate)) return false;\n        if (disabledDates === null || disabledDates === void 0 ? void 0 : disabledDates.some((disabledDate)=>(0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(disabledDate, \"yyyy-MM-dd\") === (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(date, \"yyyy-MM-dd\"))) return false;\n        if (disabledDaysOfWeek === null || disabledDaysOfWeek === void 0 ? void 0 : disabledDaysOfWeek.includes(date.getDay())) return false;\n        return true;\n    };\n    const applyTime = (date, t)=>date && t ? (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(date, {\n            hours: t.hour,\n            minutes: t.minute,\n            seconds: 0,\n            milliseconds: 0\n        }) : date;\n    const handleValueChange = (newValue)=>{\n        if (!newValue) {\n            // When a date is unselected, maintain the format consistency\n            setDateValue(mode === \"range\" ? {\n                from: undefined,\n                to: undefined\n            } : undefined);\n            setPendingSelection(undefined);\n            onChange(null);\n            return;\n        }\n        if (mode === \"range\") {\n            const { from, to } = newValue;\n            if (from && !validateDate(from)) return;\n            if (to && !validateDate(to)) return;\n            // If confirmation is required, store the selection in pending state\n            if (confirmSelection) {\n                setPendingSelection({\n                    from,\n                    to\n                });\n            } else {\n                setDateValue({\n                    from,\n                    to\n                });\n                // If time is not set, initialize it with current time\n                let currentTime = time;\n                if (!currentTime && shouldIncludeTime) {\n                    const now = new Date();\n                    currentTime = {\n                        hour: now.getHours(),\n                        minute: now.getMinutes()\n                    };\n                    setTime(currentTime);\n                }\n                let currentToTime = toTime;\n                if (!currentToTime && shouldIncludeTime && to) {\n                    const now = new Date();\n                    currentToTime = {\n                        hour: now.getHours(),\n                        minute: now.getMinutes()\n                    };\n                    setToTime(currentToTime);\n                }\n                onChange({\n                    startDate: applyTime(from, currentTime),\n                    endDate: applyTime(to, currentToTime)\n                });\n            }\n        } else {\n            const singleDate = newValue;\n            if (!validateDate(singleDate)) return;\n            // If confirmation is required, store the selection in pending state\n            if (confirmSelection) {\n                setPendingSelection(singleDate);\n            } else {\n                setDateValue(singleDate);\n                // If time is not set and we should include time, preserve existing time or initialize\n                let currentTime = time;\n                // If we have an existing dateValue with time, preserve that time\n                if (!currentTime && shouldIncludeTime && dateValue instanceof Date) {\n                    currentTime = {\n                        hour: dateValue.getHours(),\n                        minute: dateValue.getMinutes()\n                    };\n                    setTime(currentTime);\n                } else if (!currentTime && shouldIncludeTime) {\n                    // Only use current time if no existing time is available\n                    const now = new Date();\n                    currentTime = {\n                        hour: now.getHours(),\n                        minute: now.getMinutes()\n                    };\n                    setTime(currentTime);\n                }\n                // Always apply time if shouldIncludeTime is true\n                const result = shouldIncludeTime ? applyTime(singleDate, currentTime) : singleDate;\n                onChange(result);\n                if (closeOnSelect && !shouldIncludeTime) setOpen(false);\n            }\n        }\n    };\n    const handleTimeChange = function(date) {\n        let isToTime = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!date) return;\n        const newTime = {\n            hour: date.getHours(),\n            minute: date.getMinutes()\n        };\n        // Check if the time has actually changed before updating state\n        if (isToTime) {\n            const currentToTime = toTime;\n            if (!currentToTime || currentToTime.hour !== newTime.hour || currentToTime.minute !== newTime.minute) {\n                setToTime(newTime);\n                // Update pendingSelection with the new time\n                if (confirmSelection && (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.to)) {\n                    const newEndDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(pendingSelection.to, {\n                        hours: newTime.hour,\n                        minutes: newTime.minute,\n                        seconds: 0,\n                        milliseconds: 0\n                    });\n                    setPendingSelection({\n                        ...pendingSelection,\n                        to: newEndDate\n                    });\n                }\n                // If confirmation is not required, call onChange directly\n                if (!confirmSelection) {\n                    if (dateValue === null || dateValue === void 0 ? void 0 : dateValue.to) {\n                        const newEndDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(dateValue.to, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange({\n                            startDate: dateValue.from,\n                            endDate: newEndDate\n                        });\n                    }\n                }\n            }\n        } else {\n            const currentTime = time;\n            if (!currentTime || currentTime.hour !== newTime.hour || currentTime.minute !== newTime.minute) {\n                setTime(newTime);\n                // Update pendingSelection with the new time\n                if (confirmSelection) {\n                    if (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from) {\n                        const newStartDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(pendingSelection.from, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        setPendingSelection({\n                            ...pendingSelection,\n                            from: newStartDate\n                        });\n                    } else if (pendingSelection instanceof Date) {\n                        const newDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(pendingSelection, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        setPendingSelection(newDate);\n                    }\n                }\n                // If confirmation is not required, call onChange directly\n                if (!confirmSelection) {\n                    if (dateValue === null || dateValue === void 0 ? void 0 : dateValue.from) {\n                        const newStartDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(dateValue.from, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange({\n                            startDate: newStartDate,\n                            endDate: dateValue.to\n                        });\n                    } else if (dateValue instanceof Date) {\n                        const newDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(dateValue, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange(newDate);\n                    }\n                }\n            }\n        }\n    };\n    const handleClear = (e)=>{\n        e.stopPropagation() // Prevent triggering the popover\n        ;\n        setDateValue(mode === \"range\" ? {\n            from: undefined,\n            to: undefined\n        } : undefined);\n        setPendingSelection(undefined);\n        setTime(null);\n        setToTime(null);\n        onChange(null);\n    };\n    // Function to handle clear button click inside the popover\n    const handleClearInPopover = ()=>{\n        setDateValue(mode === \"range\" ? {\n            from: undefined,\n            to: undefined\n        } : undefined);\n        setPendingSelection(undefined);\n        setTime(null);\n        setToTime(null);\n        onChange(null);\n        // Close the popover after clearing\n        setOpen(false);\n    };\n    // Function to handle confirmation button click\n    const handleConfirm = ()=>{\n        if (!pendingSelection) return;\n        if (mode === \"range\") {\n            const { from, to } = pendingSelection;\n            setDateValue({\n                from,\n                to\n            });\n            // Apply time if needed\n            let currentTime = time;\n            if (!currentTime && shouldIncludeTime && from) {\n                const now = new Date();\n                currentTime = {\n                    hour: now.getHours(),\n                    minute: now.getMinutes()\n                };\n                setTime(currentTime);\n            }\n            let currentToTime = toTime;\n            if (!currentToTime && shouldIncludeTime && to) {\n                const now = new Date();\n                currentToTime = {\n                    hour: now.getHours(),\n                    minute: now.getMinutes()\n                };\n                setToTime(currentToTime);\n            }\n            onChange({\n                startDate: applyTime(from, currentTime),\n                endDate: applyTime(to, currentToTime)\n            });\n        } else {\n            const singleDate = pendingSelection;\n            setDateValue(singleDate);\n            // Apply time if needed\n            let currentTime = time;\n            // If we have an existing dateValue with time, preserve that time\n            if (!currentTime && shouldIncludeTime && dateValue instanceof Date) {\n                currentTime = {\n                    hour: dateValue.getHours(),\n                    minute: dateValue.getMinutes()\n                };\n                setTime(currentTime);\n            } else if (!currentTime && shouldIncludeTime) {\n                // Only use current time if no existing time is available\n                const now = new Date();\n                currentTime = {\n                    hour: now.getHours(),\n                    minute: now.getMinutes()\n                };\n                setTime(currentTime);\n            }\n            const result = shouldIncludeTime ? applyTime(singleDate, currentTime) : singleDate;\n            onChange(result);\n        }\n        // Close the popover after confirmation\n        setOpen(false);\n        // Reset pending selection\n        setPendingSelection(undefined);\n    };\n    // timeOptions removed as we're now using TimePicker component\n    // Determine if we should include time based on the type prop or the deprecated includeTime prop\n    const [shouldIncludeTime, setShouldIncludeTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(type === \"datetime\" || includeTime);\n    // Update shouldIncludeTime when type or includeTime changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShouldIncludeTime(type === \"datetime\" || includeTime);\n    }, [\n        type,\n        includeTime\n    ]);\n    const displayTimeFormat = shouldIncludeTime ? \"\".concat(dateFormat, \" \").concat(timeFormat) : dateFormat;\n    // Guard against invalid dates\n    const formatDateWithTime = (date)=>{\n        if (!date) return \"\";\n        const validDate = date instanceof Date ? date : new Date();\n        return isNaN(validDate.getTime()) ? \"\" : (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(validDate, displayTimeFormat);\n    };\n    // Time picker implementation now uses the TimePicker component with mode option\n    if (disabled) {\n        // Use the provided value if available, otherwise use current date\n        const displayDate = (date)=>{\n            if (!date || !(date instanceof Date) || isNaN(date.getTime())) {\n                return (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(new Date(), dateFormat);\n            }\n            return (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(date, shouldIncludeTime ? displayTimeFormat : dateFormat);\n        };\n        // Format the date based on the mode and value\n        let displayValue;\n        if (mode === \"range\") {\n            const range = dateValue;\n            if (range === null || range === void 0 ? void 0 : range.from) {\n                displayValue = range.to ? \"\".concat(displayDate(range.from), \" - \").concat(displayDate(range.to)) : displayDate(range.from);\n            } else {\n                const currentFormatted = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(new Date(), dateFormat);\n                displayValue = \"\".concat(currentFormatted, \" - \").concat(currentFormatted);\n            }\n        } else {\n            displayValue = dateValue instanceof Date ? displayDate(dateValue) : (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(new Date(), dateFormat);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                    asChild: true,\n                    id: \"date\",\n                    position: labelPosition,\n                    disabled: disabled,\n                    children: label\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                    lineNumber: 618,\n                    columnNumber: 21\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        id: \"date\",\n                        variant: \"outline\",\n                        disabled: disabled,\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-4 justify-start w-full\"),\n                        iconLeft: icon ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(icon) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(icon, {\n                            className: \"mr-[8.5px] w-5 h-5 text-neutral-400\"\n                        }) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(icon, {\n                            size: 20,\n                            className: \"mr-[8.5px] w-5 h-5 text-neutral-400\"\n                        }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"mr-[8.5px] w-5 h-5 text-neutral-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 650,\n                            columnNumber: 33\n                        }, void 0),\n                        ...buttonProps,\n                        type: \"button\",\n                        children: displayValue\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 627,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                    lineNumber: 626,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 616,\n            columnNumber: 13\n        }, undefined);\n    }\n    const renderButtonLabel = ()=>{\n        if (mode === \"range\") {\n            const range = dateValue;\n            if (range === null || range === void 0 ? void 0 : range.from) {\n                return range.to ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        formatDateWithTime(range.from),\n                        \" -\",\n                        \" \",\n                        formatDateWithTime(range.to)\n                    ]\n                }, void 0, true) : formatDateWithTime(range.from);\n            }\n            // Use consistent date format for placeholder\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-muted-foreground\",\n                children: actualPlaceholder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 677,\n                columnNumber: 17\n            }, undefined);\n        } else if (dateValue) {\n            return formatDateWithTime(dateValue);\n        }\n        // Use consistent date format for placeholder\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-muted-foreground\",\n            children: actualPlaceholder\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 686,\n            columnNumber: 13\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(wrapperClassName),\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                id: \"date\",\n                position: labelPosition,\n                disabled: disabled,\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 693,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.Popover, {\n                modal: modal,\n                open: open,\n                onOpenChange: (isOpen)=>{\n                    // When opening the popover, initialize pendingSelection with the current value\n                    if (isOpen && !pendingSelection && dateValue) {\n                        setPendingSelection(dateValue);\n                    }\n                    setOpen(isOpen);\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.PopoverTrigger, {\n                        asChild: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    id: \"date\",\n                                    variant: \"outline\",\n                                    disabled: disabled,\n                                    iconLeft: icon ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(icon) ? icon : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(icon, {\n                                        size: 20,\n                                        className: \"text-muted-foreground\"\n                                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        size: 20,\n                                        className: \"text-neutral-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 724,\n                                        columnNumber: 37\n                                    }, void 0),\n                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-4 justify-start w-full\", clearable && \"pr-10\"),\n                                    ...buttonProps,\n                                    type: \"button\",\n                                    children: renderButtonLabel()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 709,\n                                    columnNumber: 25\n                                }, undefined),\n                                clearable && (dateValue instanceof Date || (dateValue === null || dateValue === void 0 ? void 0 : dateValue.from)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"button\",\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    className: \"absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0\",\n                                    onClick: handleClear,\n                                    \"aria-label\": \"Clear date\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        size: 16,\n                                        className: \"text-neutral-400 hover:text-background0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 748,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 741,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 708,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 707,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.PopoverContent, {\n                        className: \"w-auto p-0\",\n                        align: \"start\",\n                        children: mode === \"range\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__.Calendar, {\n                                    autoFocus: true,\n                                    mode: \"range\",\n                                    month: (confirmSelection && pendingSelection ? pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from : dateValue === null || dateValue === void 0 ? void 0 : dateValue.from) || new Date(),\n                                    // allowFutureYears={allowFutureYears}\n                                    // showYearPicker={showYearPicker}\n                                    captionLayout: \"dropdown\",\n                                    selected: confirmSelection && pendingSelection ? pendingSelection : dateValue,\n                                    onSelect: (value)=>{\n                                        // Ensure we maintain the date format when a date is unselected\n                                        handleValueChange(value);\n                                    },\n                                    numberOfMonths: numberOfMonths,\n                                    showWeekNumber: showWeekNumbers,\n                                    disabled: validation ? (date)=>!validateDate(date) : undefined\n                                }, \"range\", false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 759,\n                                    columnNumber: 29\n                                }, undefined),\n                                shouldIncludeTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-t border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_time_picker__WEBPACK_IMPORTED_MODULE_5__.TimePicker, {\n                                            value: (()=>{\n                                                // When confirmation is enabled, use time from pendingSelection if available\n                                                let currentTime = time;\n                                                if (confirmSelection && (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from)) {\n                                                    const fromDate = pendingSelection.from;\n                                                    // Only use time from pendingSelection if it has meaningful time (not 00:00)\n                                                    const pendingHours = fromDate.getHours();\n                                                    const pendingMinutes = fromDate.getMinutes();\n                                                    if (pendingHours !== 0 || pendingMinutes !== 0 || !time) {\n                                                        currentTime = {\n                                                            hour: pendingHours,\n                                                            minute: pendingMinutes\n                                                        };\n                                                    }\n                                                // Otherwise, keep the existing time state\n                                                }\n                                                if (currentTime) {\n                                                    const date = new Date();\n                                                    date.setHours(currentTime.hour, currentTime.minute, 0, 0);\n                                                    return date;\n                                                }\n                                                // If no time is set, use current time and sync state\n                                                const now = new Date();\n                                                const newTime = {\n                                                    hour: now.getHours(),\n                                                    minute: now.getMinutes()\n                                                };\n                                                setTime(newTime);\n                                                return now;\n                                            })(),\n                                            toValue: (()=>{\n                                                // When confirmation is enabled, use time from pendingSelection if available\n                                                let currentToTime = toTime;\n                                                if (confirmSelection && (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.to)) {\n                                                    const toDate = pendingSelection.to;\n                                                    // Only use time from pendingSelection if it has meaningful time (not 00:00)\n                                                    const pendingHours = toDate.getHours();\n                                                    const pendingMinutes = toDate.getMinutes();\n                                                    if (pendingHours !== 0 || pendingMinutes !== 0 || !toTime) {\n                                                        currentToTime = {\n                                                            hour: pendingHours,\n                                                            minute: pendingMinutes\n                                                        };\n                                                    }\n                                                // Otherwise, keep the existing toTime state\n                                                }\n                                                if (currentToTime) {\n                                                    const date = new Date();\n                                                    date.setHours(currentToTime.hour, currentToTime.minute, 0, 0);\n                                                    return date;\n                                                }\n                                                // If no time is set, use current time and sync state\n                                                const now = new Date();\n                                                const newToTime = {\n                                                    hour: now.getHours(),\n                                                    minute: now.getMinutes()\n                                                };\n                                                setToTime(newToTime);\n                                                return now;\n                                            })(),\n                                            onChange: handleTimeChange,\n                                            onToChange: (date)=>handleTimeChange(date, true),\n                                            // Always enable time picker regardless of whether a date is selected\n                                            disabled: false,\n                                            className: \"w-full\",\n                                            mode: timeMode,\n                                            label: timeMode === \"range\" ? \"Time Range\" : undefined\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 792,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 791,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 790,\n                                    columnNumber: 33\n                                }, undefined),\n                                confirmSelection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                                            className: \"my-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 910,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-3 flex gap-3 justify-end\"),\n                                            children: [\n                                                clearable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-fit\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: handleClearInPopover,\n                                                        iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                                        className: \"w-fit\",\n                                                        \"aria-label\": \"Clear date range\",\n                                                        children: \"Clear\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                        lineNumber: 917,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 916,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: handleConfirm,\n                                                    disabled: !pendingSelection,\n                                                    iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                                    children: confirmButtonText\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 929,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 911,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 758,\n                            columnNumber: 25\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__.Calendar, {\n                                    autoFocus: true,\n                                    mode: \"single\",\n                                    month: (confirmSelection && pendingSelection ? pendingSelection : dateValue) || new Date(),\n                                    selected: confirmSelection && pendingSelection ? pendingSelection : dateValue,\n                                    onSelect: (value)=>{\n                                        // Ensure we maintain the date format when a date is unselected\n                                        handleValueChange(value);\n                                    },\n                                    captionLayout: \"dropdown\",\n                                    numberOfMonths: numberOfMonths,\n                                    showWeekNumber: showWeekNumbers,\n                                    disabled: validation ? (date)=>!validateDate(date) : undefined\n                                }, \"single\", false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 941,\n                                    columnNumber: 29\n                                }, undefined),\n                                shouldIncludeTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-t border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_time_picker__WEBPACK_IMPORTED_MODULE_5__.TimePicker, {\n                                            value: (()=>{\n                                                // When confirmation is enabled, use time from pendingSelection if available\n                                                let currentTime = time;\n                                                if (confirmSelection && pendingSelection instanceof Date && !isNaN(pendingSelection.getTime())) {\n                                                    // Only use time from pendingSelection if it has meaningful time (not 00:00)\n                                                    const pendingHours = pendingSelection.getHours();\n                                                    const pendingMinutes = pendingSelection.getMinutes();\n                                                    if (pendingHours !== 0 || pendingMinutes !== 0 || !time) {\n                                                        currentTime = {\n                                                            hour: pendingHours,\n                                                            minute: pendingMinutes\n                                                        };\n                                                    }\n                                                // Otherwise, keep the existing time state\n                                                }\n                                                if (currentTime) {\n                                                    const date = new Date();\n                                                    date.setHours(currentTime.hour, currentTime.minute, 0, 0);\n                                                    return date;\n                                                }\n                                                // If no time is set, use current time and sync state\n                                                const now = new Date();\n                                                const newTime = {\n                                                    hour: now.getHours(),\n                                                    minute: now.getMinutes()\n                                                };\n                                                setTime(newTime);\n                                                return now;\n                                            })(),\n                                            toValue: (()=>{\n                                                if (toTime) {\n                                                    const date = new Date();\n                                                    date.setHours(toTime.hour, toTime.minute, 0, 0);\n                                                    return date;\n                                                }\n                                                // If no time is set, use current time and sync state\n                                                const now = new Date();\n                                                const newToTime = {\n                                                    hour: now.getHours(),\n                                                    minute: now.getMinutes()\n                                                };\n                                                setToTime(newToTime);\n                                                return now;\n                                            })(),\n                                            onChange: handleTimeChange,\n                                            onToChange: (date)=>handleTimeChange(date, true),\n                                            // Always enable time picker regardless of whether a date is selected\n                                            disabled: false,\n                                            className: \"w-full\",\n                                            mode: timeMode,\n                                            label: timeMode === \"range\" ? \"Time Range\" : undefined\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 971,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 970,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 969,\n                                    columnNumber: 33\n                                }, undefined),\n                                confirmSelection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                                            className: \"my-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 1060,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-3 flex gap-3 justify-end\"),\n                                            children: [\n                                                clearable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-fit\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: handleClearInPopover,\n                                                        iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                                        \"aria-label\": \"Clear date\",\n                                                        children: \"Clear\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                        lineNumber: 1067,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 1066,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: handleConfirm,\n                                                    disabled: !pendingSelection,\n                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(clearable ? \"\" : \"w-full\"),\n                                                    children: confirmButtonText\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 1078,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 1061,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 940,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 756,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 697,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n        lineNumber: 691,\n        columnNumber: 9\n    }, undefined);\n};\n_s(DatePicker, \"EPGvag/dmevMVfeSbcbdVrI0y2w=\");\n_c = DatePicker;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DatePicker);\nvar _c;\n$RefreshReg$(_c, \"DatePicker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DateRange.tsx\n"));

/***/ })

});