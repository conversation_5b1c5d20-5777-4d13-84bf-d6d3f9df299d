"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/components/DateRange.tsx":
/*!**************************************!*\
  !*** ./src/components/DateRange.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui_time_picker__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/time-picker */ \"(app-pages-browser)/./src/components/ui/time-picker.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/isBefore.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/isAfter.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/set.mjs\");\n/* harmony import */ var _ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _ui_separator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst DatePicker = (param)=>{\n    let { onChange, className, placeholder = \"Select date\", mode = \"range\", type = \"date\", disabled = false, value, dateFormat = \"dd LLLL, y\", validation, numberOfMonths = mode === \"range\" ? 2 : 1, closeOnSelect = true, showWeekNumbers = false, includeTime = false, timeMode = \"single\", timeFormat = \"HH:mm\", timeInterval = 30, label, labelPosition = \"top\", clearable = false, icon, confirmSelection = true, confirmButtonText = \"Confirm\", modal = false, wrapperClassName = \"\", ...buttonProps } = param;\n    _s();\n    const [dateValue, setDateValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value || (mode === \"range\" ? {\n        from: undefined,\n        to: undefined\n    } : undefined));\n    // We'll use buttonProps directly but ensure we don't pass our custom type prop to the Button component\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [time, setTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [toTime, setToTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State to track pending selection when confirmation button is enabled\n    const [pendingSelection, setPendingSelection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value || undefined);\n    // Set placeholder based on mode\n    const actualPlaceholder = mode === \"range\" ? \"Select date range\" : \"Select date\";\n    // Optimized deep equality check for dates\n    const isDateEqual = (a, b)=>{\n        if (a === b) return true;\n        if (!a || !b) return false;\n        return a instanceof Date && b instanceof Date && a.getTime() === b.getTime();\n    };\n    const isValueEqual = (a, b)=>{\n        if (a === b) return true;\n        if (a instanceof Date && b instanceof Date) return isDateEqual(a, b);\n        if (a && b && typeof a === \"object\" && typeof b === \"object\" && \"from\" in a && \"to\" in a && \"from\" in b && \"to\" in b) {\n            return isDateEqual(a.from, b.from) && isDateEqual(a.to, b.to);\n        }\n        return false;\n    };\n    // Helper functions for time initialization\n    const getCurrentTime = ()=>{\n        const now = new Date();\n        return {\n            hour: now.getHours(),\n            minute: now.getMinutes()\n        };\n    };\n    const getTimeFromDate = (date)=>({\n            hour: date.getHours(),\n            minute: date.getMinutes()\n        });\n    const initializeTime = (existingTime, date)=>{\n        if (existingTime) return existingTime;\n        if (date && (date.getHours() || date.getMinutes())) return getTimeFromDate(date);\n        return shouldIncludeTime ? getCurrentTime() : null;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only update if the value has actually changed\n        if (!isValueEqual(value, dateValue)) {\n            if (value) {\n                setDateValue(value);\n                // Also update pendingSelection with the value\n                setPendingSelection(value);\n                if (value instanceof Date) {\n                    const timeFromDate = initializeTime(null, value);\n                    if (timeFromDate) setTime(timeFromDate);\n                } else if (\"from\" in value && value.from instanceof Date) {\n                    const { from, to } = value;\n                    const fromTime = initializeTime(null, from);\n                    if (fromTime) setTime(fromTime);\n                    if (to instanceof Date) {\n                        const toTimeFromDate = initializeTime(null, to);\n                        if (toTimeFromDate) setToTime(toTimeFromDate);\n                    }\n                }\n            } else {\n                // When value is null/undefined, reset to initial state but maintain format\n                setDateValue(mode === \"range\" ? {\n                    from: undefined,\n                    to: undefined\n                } : undefined);\n                // Also reset pendingSelection\n                setPendingSelection(undefined);\n            }\n        }\n    }, [\n        value,\n        mode\n    ]);\n    const validateDate = (date)=>{\n        if (!validation) return true;\n        const { minDate, maxDate, disabledDates, disabledDaysOfWeek } = validation;\n        if (minDate && (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_9__.isBefore)(date, minDate)) return false;\n        if (maxDate && (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_10__.isAfter)(date, maxDate)) return false;\n        if (disabledDates === null || disabledDates === void 0 ? void 0 : disabledDates.some((disabledDate)=>(0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(disabledDate, \"yyyy-MM-dd\") === (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(date, \"yyyy-MM-dd\"))) return false;\n        if (disabledDaysOfWeek === null || disabledDaysOfWeek === void 0 ? void 0 : disabledDaysOfWeek.includes(date.getDay())) return false;\n        return true;\n    };\n    const applyTime = (date, t)=>date && t ? (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(date, {\n            hours: t.hour,\n            minutes: t.minute,\n            seconds: 0,\n            milliseconds: 0\n        }) : date;\n    const handleValueChange = (newValue)=>{\n        if (!newValue) {\n            // When a date is unselected, maintain the format consistency\n            setDateValue(mode === \"range\" ? {\n                from: undefined,\n                to: undefined\n            } : undefined);\n            setPendingSelection(undefined);\n            onChange(null);\n            return;\n        }\n        if (mode === \"range\") {\n            const { from, to } = newValue;\n            if (from && !validateDate(from)) return;\n            if (to && !validateDate(to)) return;\n            // If confirmation is required, store the selection in pending state\n            if (confirmSelection) {\n                setPendingSelection({\n                    from,\n                    to\n                });\n            } else {\n                setDateValue({\n                    from,\n                    to\n                });\n                // If time is not set, initialize it with current time\n                let currentTime = time;\n                if (!currentTime && shouldIncludeTime) {\n                    const now = new Date();\n                    currentTime = {\n                        hour: now.getHours(),\n                        minute: now.getMinutes()\n                    };\n                    setTime(currentTime);\n                }\n                let currentToTime = toTime;\n                if (!currentToTime && shouldIncludeTime && to) {\n                    const now = new Date();\n                    currentToTime = {\n                        hour: now.getHours(),\n                        minute: now.getMinutes()\n                    };\n                    setToTime(currentToTime);\n                }\n                onChange({\n                    startDate: applyTime(from, currentTime),\n                    endDate: applyTime(to, currentToTime)\n                });\n            }\n        } else {\n            const singleDate = newValue;\n            if (!validateDate(singleDate)) return;\n            // If confirmation is required, store the selection in pending state\n            if (confirmSelection) {\n                setPendingSelection(singleDate);\n            } else {\n                setDateValue(singleDate);\n                // If time is not set and we should include time, preserve existing time or initialize\n                let currentTime = time;\n                // If we have an existing dateValue with time, preserve that time\n                if (!currentTime && shouldIncludeTime && dateValue instanceof Date) {\n                    currentTime = {\n                        hour: dateValue.getHours(),\n                        minute: dateValue.getMinutes()\n                    };\n                    setTime(currentTime);\n                } else if (!currentTime && shouldIncludeTime) {\n                    // Only use current time if no existing time is available\n                    const now = new Date();\n                    currentTime = {\n                        hour: now.getHours(),\n                        minute: now.getMinutes()\n                    };\n                    setTime(currentTime);\n                }\n                // Always apply time if shouldIncludeTime is true\n                const result = shouldIncludeTime ? applyTime(singleDate, currentTime) : singleDate;\n                onChange(result);\n                if (closeOnSelect && !shouldIncludeTime) setOpen(false);\n            }\n        }\n    };\n    const handleTimeChange = function(date) {\n        let isToTime = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!date) return;\n        const newTime = {\n            hour: date.getHours(),\n            minute: date.getMinutes()\n        };\n        // Check if the time has actually changed before updating state\n        if (isToTime) {\n            const currentToTime = toTime;\n            if (!currentToTime || currentToTime.hour !== newTime.hour || currentToTime.minute !== newTime.minute) {\n                setToTime(newTime);\n                // Update pendingSelection with the new time\n                if (confirmSelection && (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.to)) {\n                    const newEndDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(pendingSelection.to, {\n                        hours: newTime.hour,\n                        minutes: newTime.minute,\n                        seconds: 0,\n                        milliseconds: 0\n                    });\n                    setPendingSelection({\n                        ...pendingSelection,\n                        to: newEndDate\n                    });\n                }\n                // If confirmation is not required, call onChange directly\n                if (!confirmSelection) {\n                    if (dateValue === null || dateValue === void 0 ? void 0 : dateValue.to) {\n                        const newEndDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(dateValue.to, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange({\n                            startDate: dateValue.from,\n                            endDate: newEndDate\n                        });\n                    }\n                }\n            }\n        } else {\n            const currentTime = time;\n            if (!currentTime || currentTime.hour !== newTime.hour || currentTime.minute !== newTime.minute) {\n                setTime(newTime);\n                // Update pendingSelection with the new time\n                if (confirmSelection) {\n                    if (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from) {\n                        const newStartDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(pendingSelection.from, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        setPendingSelection({\n                            ...pendingSelection,\n                            from: newStartDate\n                        });\n                    } else if (pendingSelection instanceof Date) {\n                        const newDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(pendingSelection, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        setPendingSelection(newDate);\n                    }\n                }\n                // If confirmation is not required, call onChange directly\n                if (!confirmSelection) {\n                    if (dateValue === null || dateValue === void 0 ? void 0 : dateValue.from) {\n                        const newStartDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(dateValue.from, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange({\n                            startDate: newStartDate,\n                            endDate: dateValue.to\n                        });\n                    } else if (dateValue instanceof Date) {\n                        const newDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(dateValue, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange(newDate);\n                    }\n                }\n            }\n        }\n    };\n    const handleClear = (e)=>{\n        e.stopPropagation() // Prevent triggering the popover\n        ;\n        setDateValue(mode === \"range\" ? {\n            from: undefined,\n            to: undefined\n        } : undefined);\n        setPendingSelection(undefined);\n        setTime(null);\n        setToTime(null);\n        onChange(null);\n    };\n    // Function to handle clear button click inside the popover\n    const handleClearInPopover = ()=>{\n        setDateValue(mode === \"range\" ? {\n            from: undefined,\n            to: undefined\n        } : undefined);\n        setPendingSelection(undefined);\n        setTime(null);\n        setToTime(null);\n        onChange(null);\n        // Close the popover after clearing\n        setOpen(false);\n    };\n    // Function to handle confirmation button click\n    const handleConfirm = ()=>{\n        if (!pendingSelection) return;\n        if (mode === \"range\") {\n            const { from, to } = pendingSelection;\n            setDateValue({\n                from,\n                to\n            });\n            // Apply time if needed\n            let currentTime = time;\n            if (!currentTime && shouldIncludeTime && from) {\n                const now = new Date();\n                currentTime = {\n                    hour: now.getHours(),\n                    minute: now.getMinutes()\n                };\n                setTime(currentTime);\n            }\n            let currentToTime = toTime;\n            if (!currentToTime && shouldIncludeTime && to) {\n                const now = new Date();\n                currentToTime = {\n                    hour: now.getHours(),\n                    minute: now.getMinutes()\n                };\n                setToTime(currentToTime);\n            }\n            onChange({\n                startDate: applyTime(from, currentTime),\n                endDate: applyTime(to, currentToTime)\n            });\n        } else {\n            const singleDate = pendingSelection;\n            setDateValue(singleDate);\n            // Apply time if needed\n            let currentTime = time;\n            // If we have an existing dateValue with time, preserve that time\n            if (!currentTime && shouldIncludeTime && dateValue instanceof Date) {\n                currentTime = {\n                    hour: dateValue.getHours(),\n                    minute: dateValue.getMinutes()\n                };\n                setTime(currentTime);\n            } else if (!currentTime && shouldIncludeTime) {\n                // Only use current time if no existing time is available\n                const now = new Date();\n                currentTime = {\n                    hour: now.getHours(),\n                    minute: now.getMinutes()\n                };\n                setTime(currentTime);\n            }\n            const result = shouldIncludeTime ? applyTime(singleDate, currentTime) : singleDate;\n            onChange(result);\n        }\n        // Close the popover after confirmation\n        setOpen(false);\n        // Reset pending selection\n        setPendingSelection(undefined);\n    };\n    // timeOptions removed as we're now using TimePicker component\n    // Determine if we should include time based on the type prop or the deprecated includeTime prop\n    const [shouldIncludeTime, setShouldIncludeTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(type === \"datetime\" || includeTime);\n    // Update shouldIncludeTime when type or includeTime changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShouldIncludeTime(type === \"datetime\" || includeTime);\n    }, [\n        type,\n        includeTime\n    ]);\n    const displayTimeFormat = shouldIncludeTime ? \"\".concat(dateFormat, \" \").concat(timeFormat) : dateFormat;\n    // Guard against invalid dates\n    const formatDateWithTime = (date)=>{\n        if (!date) return \"\";\n        const validDate = date instanceof Date ? date : new Date();\n        return isNaN(validDate.getTime()) ? \"\" : (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(validDate, displayTimeFormat);\n    };\n    // Time picker implementation now uses the TimePicker component with mode option\n    if (disabled) {\n        // Use the provided value if available, otherwise use current date\n        const displayDate = (date)=>{\n            if (!date || !(date instanceof Date) || isNaN(date.getTime())) {\n                return (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(new Date(), dateFormat);\n            }\n            return (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(date, shouldIncludeTime ? displayTimeFormat : dateFormat);\n        };\n        // Format the date based on the mode and value\n        let displayValue;\n        if (mode === \"range\") {\n            const range = dateValue;\n            if (range === null || range === void 0 ? void 0 : range.from) {\n                displayValue = range.to ? \"\".concat(displayDate(range.from), \" - \").concat(displayDate(range.to)) : displayDate(range.from);\n            } else {\n                const currentFormatted = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(new Date(), dateFormat);\n                displayValue = \"\".concat(currentFormatted, \" - \").concat(currentFormatted);\n            }\n        } else {\n            displayValue = dateValue instanceof Date ? displayDate(dateValue) : (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(new Date(), dateFormat);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                    asChild: true,\n                    id: \"date\",\n                    position: labelPosition,\n                    disabled: disabled,\n                    children: label\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                    lineNumber: 633,\n                    columnNumber: 21\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        id: \"date\",\n                        variant: \"outline\",\n                        disabled: disabled,\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-4 justify-start w-full\"),\n                        iconLeft: icon ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(icon) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(icon, {\n                            className: \"mr-[8.5px] w-5 h-5 text-neutral-400\"\n                        }) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(icon, {\n                            size: 20,\n                            className: \"mr-[8.5px] w-5 h-5 text-neutral-400\"\n                        }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"mr-[8.5px] w-5 h-5 text-neutral-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 665,\n                            columnNumber: 33\n                        }, void 0),\n                        ...buttonProps,\n                        type: \"button\",\n                        children: displayValue\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 642,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                    lineNumber: 641,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 631,\n            columnNumber: 13\n        }, undefined);\n    }\n    const renderButtonLabel = ()=>{\n        if (mode === \"range\") {\n            const range = dateValue;\n            if (range === null || range === void 0 ? void 0 : range.from) {\n                return range.to ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        formatDateWithTime(range.from),\n                        \" -\",\n                        \" \",\n                        formatDateWithTime(range.to)\n                    ]\n                }, void 0, true) : formatDateWithTime(range.from);\n            }\n            // Use consistent date format for placeholder\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-muted-foreground\",\n                children: actualPlaceholder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 692,\n                columnNumber: 17\n            }, undefined);\n        } else if (dateValue) {\n            return formatDateWithTime(dateValue);\n        }\n        // Use consistent date format for placeholder\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-muted-foreground\",\n            children: actualPlaceholder\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 701,\n            columnNumber: 13\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(wrapperClassName),\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                id: \"date\",\n                position: labelPosition,\n                disabled: disabled,\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 708,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.Popover, {\n                modal: modal,\n                open: open,\n                onOpenChange: (isOpen)=>{\n                    // When opening the popover, initialize pendingSelection with the current value\n                    if (isOpen && !pendingSelection && dateValue) {\n                        setPendingSelection(dateValue);\n                    }\n                    setOpen(isOpen);\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.PopoverTrigger, {\n                        asChild: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    id: \"date\",\n                                    variant: \"outline\",\n                                    disabled: disabled,\n                                    iconLeft: icon ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(icon) ? icon : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(icon, {\n                                        size: 20,\n                                        className: \"text-muted-foreground\"\n                                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        size: 20,\n                                        className: \"text-neutral-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 739,\n                                        columnNumber: 37\n                                    }, void 0),\n                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-4 justify-start w-full\", clearable && \"pr-10\"),\n                                    ...buttonProps,\n                                    type: \"button\",\n                                    children: renderButtonLabel()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 724,\n                                    columnNumber: 25\n                                }, undefined),\n                                clearable && (dateValue instanceof Date || (dateValue === null || dateValue === void 0 ? void 0 : dateValue.from)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"button\",\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    className: \"absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0\",\n                                    onClick: handleClear,\n                                    \"aria-label\": \"Clear date\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        size: 16,\n                                        className: \"text-neutral-400 hover:text-background0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 763,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 756,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 723,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 722,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.PopoverContent, {\n                        className: \"w-auto p-0\",\n                        align: \"start\",\n                        children: mode === \"range\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__.Calendar, {\n                                    autoFocus: true,\n                                    mode: \"range\",\n                                    month: (confirmSelection && pendingSelection ? pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from : dateValue === null || dateValue === void 0 ? void 0 : dateValue.from) || new Date(),\n                                    // allowFutureYears={allowFutureYears}\n                                    // showYearPicker={showYearPicker}\n                                    captionLayout: \"dropdown\",\n                                    selected: confirmSelection && pendingSelection ? pendingSelection : dateValue,\n                                    onSelect: (value)=>{\n                                        // Ensure we maintain the date format when a date is unselected\n                                        handleValueChange(value);\n                                    },\n                                    numberOfMonths: numberOfMonths,\n                                    showWeekNumber: showWeekNumbers,\n                                    disabled: validation ? (date)=>!validateDate(date) : undefined\n                                }, \"range\", false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 774,\n                                    columnNumber: 29\n                                }, undefined),\n                                shouldIncludeTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-t border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_time_picker__WEBPACK_IMPORTED_MODULE_5__.TimePicker, {\n                                            value: (()=>{\n                                                // When confirmation is enabled, use time from pendingSelection if available\n                                                let currentTime = time;\n                                                if (confirmSelection && (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from)) {\n                                                    const fromDate = pendingSelection.from;\n                                                    // Only use time from pendingSelection if it has meaningful time (not 00:00)\n                                                    const pendingHours = fromDate.getHours();\n                                                    const pendingMinutes = fromDate.getMinutes();\n                                                    if (pendingHours !== 0 || pendingMinutes !== 0 || !time) {\n                                                        currentTime = {\n                                                            hour: pendingHours,\n                                                            minute: pendingMinutes\n                                                        };\n                                                    }\n                                                // Otherwise, keep the existing time state\n                                                }\n                                                if (currentTime) {\n                                                    const date = new Date();\n                                                    date.setHours(currentTime.hour, currentTime.minute, 0, 0);\n                                                    return date;\n                                                }\n                                                // If no time is set, use current time and sync state\n                                                const now = new Date();\n                                                const newTime = {\n                                                    hour: now.getHours(),\n                                                    minute: now.getMinutes()\n                                                };\n                                                setTime(newTime);\n                                                return now;\n                                            })(),\n                                            toValue: (()=>{\n                                                // When confirmation is enabled, use time from pendingSelection if available\n                                                let currentToTime = toTime;\n                                                if (confirmSelection && (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.to)) {\n                                                    const toDate = pendingSelection.to;\n                                                    // Only use time from pendingSelection if it has meaningful time (not 00:00)\n                                                    const pendingHours = toDate.getHours();\n                                                    const pendingMinutes = toDate.getMinutes();\n                                                    if (pendingHours !== 0 || pendingMinutes !== 0 || !toTime) {\n                                                        currentToTime = {\n                                                            hour: pendingHours,\n                                                            minute: pendingMinutes\n                                                        };\n                                                    }\n                                                // Otherwise, keep the existing toTime state\n                                                }\n                                                if (currentToTime) {\n                                                    const date = new Date();\n                                                    date.setHours(currentToTime.hour, currentToTime.minute, 0, 0);\n                                                    return date;\n                                                }\n                                                // If no time is set, use current time and sync state\n                                                const now = new Date();\n                                                const newToTime = {\n                                                    hour: now.getHours(),\n                                                    minute: now.getMinutes()\n                                                };\n                                                setToTime(newToTime);\n                                                return now;\n                                            })(),\n                                            onChange: handleTimeChange,\n                                            onToChange: (date)=>handleTimeChange(date, true),\n                                            // Always enable time picker regardless of whether a date is selected\n                                            disabled: false,\n                                            className: \"w-full\",\n                                            mode: timeMode,\n                                            label: timeMode === \"range\" ? \"Time Range\" : undefined\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 807,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 806,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 805,\n                                    columnNumber: 33\n                                }, undefined),\n                                confirmSelection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                                            className: \"my-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 925,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-3 flex gap-3 justify-end\"),\n                                            children: [\n                                                clearable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-fit\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: handleClearInPopover,\n                                                        iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                                        className: \"w-fit\",\n                                                        \"aria-label\": \"Clear date range\",\n                                                        children: \"Clear\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                        lineNumber: 932,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 931,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: handleConfirm,\n                                                    disabled: !pendingSelection,\n                                                    iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                                    children: confirmButtonText\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 944,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 926,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 773,\n                            columnNumber: 25\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__.Calendar, {\n                                    autoFocus: true,\n                                    mode: \"single\",\n                                    month: (confirmSelection && pendingSelection ? pendingSelection : dateValue) || new Date(),\n                                    selected: confirmSelection && pendingSelection ? pendingSelection : dateValue,\n                                    onSelect: (value)=>{\n                                        // Ensure we maintain the date format when a date is unselected\n                                        handleValueChange(value);\n                                    },\n                                    captionLayout: \"dropdown\",\n                                    numberOfMonths: numberOfMonths,\n                                    showWeekNumber: showWeekNumbers,\n                                    disabled: validation ? (date)=>!validateDate(date) : undefined\n                                }, \"single\", false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 956,\n                                    columnNumber: 29\n                                }, undefined),\n                                shouldIncludeTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-t border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_time_picker__WEBPACK_IMPORTED_MODULE_5__.TimePicker, {\n                                            value: (()=>{\n                                                // When confirmation is enabled, use time from pendingSelection if available\n                                                let currentTime = time;\n                                                if (confirmSelection && pendingSelection instanceof Date && !isNaN(pendingSelection.getTime())) {\n                                                    // Only use time from pendingSelection if it has meaningful time (not 00:00)\n                                                    const pendingHours = pendingSelection.getHours();\n                                                    const pendingMinutes = pendingSelection.getMinutes();\n                                                    if (pendingHours !== 0 || pendingMinutes !== 0 || !time) {\n                                                        currentTime = {\n                                                            hour: pendingHours,\n                                                            minute: pendingMinutes\n                                                        };\n                                                    }\n                                                // Otherwise, keep the existing time state\n                                                }\n                                                if (currentTime) {\n                                                    const date = new Date();\n                                                    date.setHours(currentTime.hour, currentTime.minute, 0, 0);\n                                                    return date;\n                                                }\n                                                // If no time is set, use current time and sync state\n                                                const now = new Date();\n                                                const newTime = {\n                                                    hour: now.getHours(),\n                                                    minute: now.getMinutes()\n                                                };\n                                                setTime(newTime);\n                                                return now;\n                                            })(),\n                                            toValue: (()=>{\n                                                if (toTime) {\n                                                    const date = new Date();\n                                                    date.setHours(toTime.hour, toTime.minute, 0, 0);\n                                                    return date;\n                                                }\n                                                // If no time is set, use current time and sync state\n                                                const now = new Date();\n                                                const newToTime = {\n                                                    hour: now.getHours(),\n                                                    minute: now.getMinutes()\n                                                };\n                                                setToTime(newToTime);\n                                                return now;\n                                            })(),\n                                            onChange: handleTimeChange,\n                                            onToChange: (date)=>handleTimeChange(date, true),\n                                            // Always enable time picker regardless of whether a date is selected\n                                            disabled: false,\n                                            className: \"w-full\",\n                                            mode: timeMode,\n                                            label: timeMode === \"range\" ? \"Time Range\" : undefined\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 986,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 985,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 984,\n                                    columnNumber: 33\n                                }, undefined),\n                                confirmSelection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                                            className: \"my-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 1075,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-3 flex gap-3 justify-end\"),\n                                            children: [\n                                                clearable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-fit\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: handleClearInPopover,\n                                                        iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                                        \"aria-label\": \"Clear date\",\n                                                        children: \"Clear\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                        lineNumber: 1082,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 1081,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: handleConfirm,\n                                                    disabled: !pendingSelection,\n                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(clearable ? \"\" : \"w-full\"),\n                                                    children: confirmButtonText\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 1093,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 1076,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 955,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 771,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 712,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n        lineNumber: 706,\n        columnNumber: 9\n    }, undefined);\n};\n_s(DatePicker, \"EPGvag/dmevMVfeSbcbdVrI0y2w=\");\n_c = DatePicker;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DatePicker);\nvar _c;\n$RefreshReg$(_c, \"DatePicker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DateRange.tsx\n"));

/***/ })

});