"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/vessel/info/page",{

/***/ "(app-pages-browser)/./src/components/DateRange.tsx":
/*!**************************************!*\
  !*** ./src/components/DateRange.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui_time_picker__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/time-picker */ \"(app-pages-browser)/./src/components/ui/time-picker.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/isBefore.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/isAfter.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/set.mjs\");\n/* harmony import */ var _ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _ui_separator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst DatePicker = (param)=>{\n    let { onChange, className, placeholder = \"Select date\", mode = \"range\", type = \"date\", disabled = false, value, dateFormat = \"dd LLLL, y\", validation, numberOfMonths = mode === \"range\" ? 2 : 1, closeOnSelect = true, showWeekNumbers = false, includeTime = false, timeMode = \"single\", timeFormat = \"HH:mm\", timeInterval = 30, label, labelPosition = \"top\", clearable = false, icon, confirmSelection = true, confirmButtonText = \"Confirm\", modal = false, wrapperClassName = \"\", ...buttonProps } = param;\n    _s();\n    const [dateValue, setDateValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value || (mode === \"range\" ? {\n        from: undefined,\n        to: undefined\n    } : undefined));\n    // We'll use buttonProps directly but ensure we don't pass our custom type prop to the Button component\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [time, setTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [toTime, setToTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State to track pending selection when confirmation button is enabled\n    const [pendingSelection, setPendingSelection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value || undefined);\n    // Set placeholder based on mode\n    const actualPlaceholder = mode === \"range\" ? \"Select date range\" : \"Select date\";\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Deep equality check for dates to prevent unnecessary updates\n        const isEqual = (a, b)=>{\n            if (a === b) return true;\n            // Handle Date objects\n            if (a instanceof Date && b instanceof Date) {\n                return a.getTime() === b.getTime();\n            }\n            // Handle DateRange objects\n            if (a && b && typeof a === \"object\" && typeof b === \"object\") {\n                if (\"from\" in a && \"to\" in a && \"from\" in b && \"to\" in b) {\n                    const fromEqual = a.from && b.from ? a.from instanceof Date && b.from instanceof Date && a.from.getTime() === b.from.getTime() : a.from === b.from;\n                    const toEqual = a.to && b.to ? a.to instanceof Date && b.to instanceof Date && a.to.getTime() === b.to.getTime() : a.to === b.to;\n                    return fromEqual && toEqual;\n                }\n            }\n            return false;\n        };\n        // Only update if the value has actually changed\n        if (!isEqual(value, dateValue)) {\n            if (value) {\n                setDateValue(value);\n                // Also update pendingSelection with the value\n                setPendingSelection(value);\n                if (value instanceof Date) {\n                    const hours = value.getHours();\n                    const minutes = value.getMinutes();\n                    if (hours || minutes) setTime({\n                        hour: hours,\n                        minute: minutes\n                    });\n                } else if (\"from\" in value && value.from instanceof Date) {\n                    const { from, to } = value;\n                    const fromHours = from.getHours();\n                    const fromMinutes = from.getMinutes();\n                    if (fromHours || fromMinutes) setTime({\n                        hour: fromHours,\n                        minute: fromMinutes\n                    });\n                    if (to instanceof Date) {\n                        const toHours = to.getHours();\n                        const toMinutes = to.getMinutes();\n                        if (toHours || toMinutes) setToTime({\n                            hour: toHours,\n                            minute: toMinutes\n                        });\n                    }\n                }\n            } else {\n                // When value is null/undefined, reset to initial state but maintain format\n                setDateValue(mode === \"range\" ? {\n                    from: undefined,\n                    to: undefined\n                } : undefined);\n                // Also reset pendingSelection\n                setPendingSelection(undefined);\n            }\n        }\n    }, [\n        value,\n        mode\n    ]);\n    const validateDate = (date)=>{\n        if (!validation) return true;\n        const { minDate, maxDate, disabledDates, disabledDaysOfWeek } = validation;\n        if (minDate && (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_9__.isBefore)(date, minDate)) return false;\n        if (maxDate && (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_10__.isAfter)(date, maxDate)) return false;\n        if (disabledDates === null || disabledDates === void 0 ? void 0 : disabledDates.some((disabledDate)=>(0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(disabledDate, \"yyyy-MM-dd\") === (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(date, \"yyyy-MM-dd\"))) return false;\n        if (disabledDaysOfWeek === null || disabledDaysOfWeek === void 0 ? void 0 : disabledDaysOfWeek.includes(date.getDay())) return false;\n        return true;\n    };\n    const applyTime = (date, t)=>date && t ? (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(date, {\n            hours: t.hour,\n            minutes: t.minute,\n            seconds: 0,\n            milliseconds: 0\n        }) : date;\n    const handleValueChange = (newValue)=>{\n        if (!newValue) {\n            // When a date is unselected, maintain the format consistency\n            setDateValue(mode === \"range\" ? {\n                from: undefined,\n                to: undefined\n            } : undefined);\n            setPendingSelection(undefined);\n            onChange(null);\n            return;\n        }\n        if (mode === \"range\") {\n            const { from, to } = newValue;\n            if (from && !validateDate(from)) return;\n            if (to && !validateDate(to)) return;\n            // If confirmation is required, store the selection in pending state\n            if (confirmSelection) {\n                setPendingSelection({\n                    from,\n                    to\n                });\n            } else {\n                setDateValue({\n                    from,\n                    to\n                });\n                // If time is not set, initialize it with current time\n                let currentTime = time;\n                if (!currentTime && shouldIncludeTime) {\n                    const now = new Date();\n                    currentTime = {\n                        hour: now.getHours(),\n                        minute: now.getMinutes()\n                    };\n                    setTime(currentTime);\n                }\n                let currentToTime = toTime;\n                if (!currentToTime && shouldIncludeTime && to) {\n                    const now = new Date();\n                    currentToTime = {\n                        hour: now.getHours(),\n                        minute: now.getMinutes()\n                    };\n                    setToTime(currentToTime);\n                }\n                onChange({\n                    startDate: applyTime(from, currentTime),\n                    endDate: applyTime(to, currentToTime)\n                });\n            }\n        } else {\n            const singleDate = newValue;\n            if (!validateDate(singleDate)) return;\n            // If confirmation is required, store the selection in pending state\n            if (confirmSelection) {\n                setPendingSelection(singleDate);\n            } else {\n                setDateValue(singleDate);\n                // If time is not set and we should include time, preserve existing time or initialize\n                let currentTime = time;\n                // If we have an existing dateValue with time, preserve that time\n                if (!currentTime && shouldIncludeTime && dateValue instanceof Date) {\n                    currentTime = {\n                        hour: dateValue.getHours(),\n                        minute: dateValue.getMinutes()\n                    };\n                    setTime(currentTime);\n                } else if (!currentTime && shouldIncludeTime) {\n                    // Only use current time if no existing time is available\n                    const now = new Date();\n                    currentTime = {\n                        hour: now.getHours(),\n                        minute: now.getMinutes()\n                    };\n                    setTime(currentTime);\n                }\n                // Always apply time if shouldIncludeTime is true\n                const result = shouldIncludeTime ? applyTime(singleDate, currentTime) : singleDate;\n                onChange(result);\n                if (closeOnSelect && !shouldIncludeTime) setOpen(false);\n            }\n        }\n    };\n    const handleTimeChange = function(date) {\n        let isToTime = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!date) return;\n        const newTime = {\n            hour: date.getHours(),\n            minute: date.getMinutes()\n        };\n        // Check if the time has actually changed before updating state\n        if (isToTime) {\n            const currentToTime = toTime;\n            if (!currentToTime || currentToTime.hour !== newTime.hour || currentToTime.minute !== newTime.minute) {\n                setToTime(newTime);\n                // Update pendingSelection with the new time\n                if (confirmSelection && (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.to)) {\n                    const newEndDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(pendingSelection.to, {\n                        hours: newTime.hour,\n                        minutes: newTime.minute,\n                        seconds: 0,\n                        milliseconds: 0\n                    });\n                    setPendingSelection({\n                        ...pendingSelection,\n                        to: newEndDate\n                    });\n                }\n                // If confirmation is not required, call onChange directly\n                if (!confirmSelection) {\n                    if (dateValue === null || dateValue === void 0 ? void 0 : dateValue.to) {\n                        const newEndDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(dateValue.to, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange({\n                            startDate: dateValue.from,\n                            endDate: newEndDate\n                        });\n                    }\n                }\n            }\n        } else {\n            const currentTime = time;\n            if (!currentTime || currentTime.hour !== newTime.hour || currentTime.minute !== newTime.minute) {\n                setTime(newTime);\n                // Update pendingSelection with the new time\n                if (confirmSelection) {\n                    if (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from) {\n                        const newStartDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(pendingSelection.from, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        setPendingSelection({\n                            ...pendingSelection,\n                            from: newStartDate\n                        });\n                    } else if (pendingSelection instanceof Date) {\n                        const newDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(pendingSelection, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        setPendingSelection(newDate);\n                    }\n                }\n                // If confirmation is not required, call onChange directly\n                if (!confirmSelection) {\n                    if (dateValue === null || dateValue === void 0 ? void 0 : dateValue.from) {\n                        const newStartDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(dateValue.from, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange({\n                            startDate: newStartDate,\n                            endDate: dateValue.to\n                        });\n                    } else if (dateValue instanceof Date) {\n                        const newDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(dateValue, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange(newDate);\n                    }\n                }\n            }\n        }\n    };\n    const handleClear = (e)=>{\n        e.stopPropagation() // Prevent triggering the popover\n        ;\n        setDateValue(mode === \"range\" ? {\n            from: undefined,\n            to: undefined\n        } : undefined);\n        setPendingSelection(undefined);\n        setTime(null);\n        setToTime(null);\n        onChange(null);\n    };\n    // Function to handle clear button click inside the popover\n    const handleClearInPopover = ()=>{\n        setDateValue(mode === \"range\" ? {\n            from: undefined,\n            to: undefined\n        } : undefined);\n        setPendingSelection(undefined);\n        setTime(null);\n        setToTime(null);\n        onChange(null);\n        // Close the popover after clearing\n        setOpen(false);\n    };\n    // Function to handle confirmation button click\n    const handleConfirm = ()=>{\n        if (!pendingSelection) return;\n        if (mode === \"range\") {\n            const { from, to } = pendingSelection;\n            setDateValue({\n                from,\n                to\n            });\n            // Apply time if needed\n            let currentTime = time;\n            if (!currentTime && shouldIncludeTime && from) {\n                const now = new Date();\n                currentTime = {\n                    hour: now.getHours(),\n                    minute: now.getMinutes()\n                };\n                setTime(currentTime);\n            }\n            let currentToTime = toTime;\n            if (!currentToTime && shouldIncludeTime && to) {\n                const now = new Date();\n                currentToTime = {\n                    hour: now.getHours(),\n                    minute: now.getMinutes()\n                };\n                setToTime(currentToTime);\n            }\n            onChange({\n                startDate: applyTime(from, currentTime),\n                endDate: applyTime(to, currentToTime)\n            });\n        } else {\n            const singleDate = pendingSelection;\n            setDateValue(singleDate);\n            // Apply time if needed\n            let currentTime = time;\n            // If we have an existing dateValue with time, preserve that time\n            if (!currentTime && shouldIncludeTime && dateValue instanceof Date) {\n                currentTime = {\n                    hour: dateValue.getHours(),\n                    minute: dateValue.getMinutes()\n                };\n                setTime(currentTime);\n            } else if (!currentTime && shouldIncludeTime) {\n                // Only use current time if no existing time is available\n                const now = new Date();\n                currentTime = {\n                    hour: now.getHours(),\n                    minute: now.getMinutes()\n                };\n                setTime(currentTime);\n            }\n            const result = shouldIncludeTime ? applyTime(singleDate, currentTime) : singleDate;\n            onChange(result);\n        }\n        // Close the popover after confirmation\n        setOpen(false);\n        // Reset pending selection\n        setPendingSelection(undefined);\n    };\n    // timeOptions removed as we're now using TimePicker component\n    // Determine if we should include time based on the type prop or the deprecated includeTime prop\n    const [shouldIncludeTime, setShouldIncludeTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(type === \"datetime\" || includeTime);\n    // Update shouldIncludeTime when type or includeTime changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShouldIncludeTime(type === \"datetime\" || includeTime);\n    }, [\n        type,\n        includeTime\n    ]);\n    const displayTimeFormat = shouldIncludeTime ? \"\".concat(dateFormat, \" \").concat(timeFormat) : dateFormat;\n    // Guard against invalid dates\n    const formatDateWithTime = (date)=>{\n        if (!date) return \"\";\n        const validDate = date instanceof Date ? date : new Date();\n        return isNaN(validDate.getTime()) ? \"\" : (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(validDate, displayTimeFormat);\n    };\n    // Time picker implementation now uses the TimePicker component with mode option\n    if (disabled) {\n        // Use the provided value if available, otherwise use current date\n        const displayDate = (date)=>{\n            if (!date || !(date instanceof Date) || isNaN(date.getTime())) {\n                return (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(new Date(), dateFormat);\n            }\n            return (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(date, shouldIncludeTime ? displayTimeFormat : dateFormat);\n        };\n        // Format the date based on the mode and value\n        let displayValue;\n        if (mode === \"range\") {\n            const range = dateValue;\n            if (range === null || range === void 0 ? void 0 : range.from) {\n                displayValue = range.to ? \"\".concat(displayDate(range.from), \" - \").concat(displayDate(range.to)) : displayDate(range.from);\n            } else {\n                const currentFormatted = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(new Date(), dateFormat);\n                displayValue = \"\".concat(currentFormatted, \" - \").concat(currentFormatted);\n            }\n        } else {\n            displayValue = dateValue instanceof Date ? displayDate(dateValue) : (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(new Date(), dateFormat);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                    asChild: true,\n                    id: \"date\",\n                    position: labelPosition,\n                    disabled: disabled,\n                    children: label\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                    lineNumber: 623,\n                    columnNumber: 21\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        id: \"date\",\n                        variant: \"outline\",\n                        disabled: disabled,\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-4 justify-start w-full\"),\n                        iconLeft: icon ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(icon) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(icon, {\n                            className: \"mr-[8.5px] w-5 h-5 text-neutral-400\"\n                        }) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(icon, {\n                            size: 20,\n                            className: \"mr-[8.5px] w-5 h-5 text-neutral-400\"\n                        }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"mr-[8.5px] w-5 h-5 text-neutral-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 655,\n                            columnNumber: 33\n                        }, void 0),\n                        ...buttonProps,\n                        type: \"button\",\n                        children: displayValue\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 632,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                    lineNumber: 631,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 621,\n            columnNumber: 13\n        }, undefined);\n    }\n    const renderButtonLabel = ()=>{\n        if (mode === \"range\") {\n            const range = dateValue;\n            if (range === null || range === void 0 ? void 0 : range.from) {\n                return range.to ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        formatDateWithTime(range.from),\n                        \" -\",\n                        \" \",\n                        formatDateWithTime(range.to)\n                    ]\n                }, void 0, true) : formatDateWithTime(range.from);\n            }\n            // Use consistent date format for placeholder\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-muted-foreground\",\n                children: actualPlaceholder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 682,\n                columnNumber: 17\n            }, undefined);\n        } else if (dateValue) {\n            return formatDateWithTime(dateValue);\n        }\n        // Use consistent date format for placeholder\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-muted-foreground\",\n            children: actualPlaceholder\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 691,\n            columnNumber: 13\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(wrapperClassName),\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                id: \"date\",\n                position: labelPosition,\n                disabled: disabled,\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 698,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.Popover, {\n                modal: modal,\n                open: open,\n                onOpenChange: (isOpen)=>{\n                    // When opening the popover, initialize pendingSelection with the current value\n                    if (isOpen && !pendingSelection && dateValue) {\n                        setPendingSelection(dateValue);\n                    }\n                    setOpen(isOpen);\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.PopoverTrigger, {\n                        asChild: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    id: \"date\",\n                                    variant: \"outline\",\n                                    disabled: disabled,\n                                    iconLeft: icon ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(icon) ? icon : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(icon, {\n                                        size: 20,\n                                        className: \"text-muted-foreground\"\n                                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        size: 20,\n                                        className: \"text-neutral-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 729,\n                                        columnNumber: 37\n                                    }, void 0),\n                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-4 justify-start w-full\", clearable && \"pr-10\"),\n                                    ...buttonProps,\n                                    type: \"button\",\n                                    children: renderButtonLabel()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 714,\n                                    columnNumber: 25\n                                }, undefined),\n                                clearable && (dateValue instanceof Date || (dateValue === null || dateValue === void 0 ? void 0 : dateValue.from)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"button\",\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    className: \"absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0\",\n                                    onClick: handleClear,\n                                    \"aria-label\": \"Clear date\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        size: 16,\n                                        className: \"text-neutral-400 hover:text-background0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 753,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 746,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 713,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 712,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.PopoverContent, {\n                        className: \"w-auto p-0\",\n                        align: \"start\",\n                        children: mode === \"range\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__.Calendar, {\n                                    autoFocus: true,\n                                    mode: \"range\",\n                                    month: (confirmSelection && pendingSelection ? pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from : dateValue === null || dateValue === void 0 ? void 0 : dateValue.from) || new Date(),\n                                    // allowFutureYears={allowFutureYears}\n                                    // showYearPicker={showYearPicker}\n                                    captionLayout: \"dropdown\",\n                                    selected: confirmSelection && pendingSelection ? pendingSelection : dateValue,\n                                    onSelect: (value)=>{\n                                        // Ensure we maintain the date format when a date is unselected\n                                        handleValueChange(value);\n                                    },\n                                    numberOfMonths: numberOfMonths,\n                                    showWeekNumber: showWeekNumbers,\n                                    disabled: validation ? (date)=>!validateDate(date) : undefined\n                                }, \"range\", false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 764,\n                                    columnNumber: 29\n                                }, undefined),\n                                shouldIncludeTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-t border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_time_picker__WEBPACK_IMPORTED_MODULE_5__.TimePicker, {\n                                            value: (()=>{\n                                                // When confirmation is enabled, use time from pendingSelection if available\n                                                let currentTime = time;\n                                                if (confirmSelection && (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from)) {\n                                                    const fromDate = pendingSelection.from;\n                                                    currentTime = {\n                                                        hour: fromDate.getHours(),\n                                                        minute: fromDate.getMinutes()\n                                                    };\n                                                }\n                                                if (currentTime) {\n                                                    const date = new Date();\n                                                    date.setHours(currentTime.hour, currentTime.minute, 0, 0);\n                                                    return date;\n                                                }\n                                                // If no time is set, use current time and sync state\n                                                const now = new Date();\n                                                const newTime = {\n                                                    hour: now.getHours(),\n                                                    minute: now.getMinutes()\n                                                };\n                                                setTime(newTime);\n                                                return now;\n                                            })(),\n                                            toValue: (()=>{\n                                                // When confirmation is enabled, use time from pendingSelection if available\n                                                let currentToTime = toTime;\n                                                if (confirmSelection && (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.to)) {\n                                                    const toDate = pendingSelection.to;\n                                                    currentToTime = {\n                                                        hour: toDate.getHours(),\n                                                        minute: toDate.getMinutes()\n                                                    };\n                                                }\n                                                if (currentToTime) {\n                                                    const date = new Date();\n                                                    date.setHours(currentToTime.hour, currentToTime.minute, 0, 0);\n                                                    return date;\n                                                }\n                                                return undefined;\n                                            })(),\n                                            onChange: handleTimeChange,\n                                            onToChange: (date)=>handleTimeChange(date, true),\n                                            // Always enable time picker regardless of whether a date is selected\n                                            disabled: false,\n                                            className: \"w-full\",\n                                            mode: timeMode,\n                                            label: timeMode === \"range\" ? \"Time Range\" : undefined\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 797,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 796,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 795,\n                                    columnNumber: 33\n                                }, undefined),\n                                confirmSelection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                                            className: \"my-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 884,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-3 flex gap-3 justify-end\"),\n                                            children: [\n                                                clearable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-fit\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: handleClearInPopover,\n                                                        iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                                        className: \"w-fit\",\n                                                        \"aria-label\": \"Clear date range\",\n                                                        children: \"Clear\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                        lineNumber: 891,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 890,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: handleConfirm,\n                                                    disabled: !pendingSelection,\n                                                    iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                                    children: confirmButtonText\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 903,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 885,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 763,\n                            columnNumber: 25\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__.Calendar, {\n                                    autoFocus: true,\n                                    mode: \"single\",\n                                    month: (confirmSelection && pendingSelection ? pendingSelection : dateValue) || new Date(),\n                                    selected: confirmSelection && pendingSelection ? pendingSelection : dateValue,\n                                    onSelect: (value)=>{\n                                        // Ensure we maintain the date format when a date is unselected\n                                        handleValueChange(value);\n                                    },\n                                    captionLayout: \"dropdown\",\n                                    numberOfMonths: numberOfMonths,\n                                    showWeekNumber: showWeekNumbers,\n                                    disabled: validation ? (date)=>!validateDate(date) : undefined\n                                }, \"single\", false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 915,\n                                    columnNumber: 29\n                                }, undefined),\n                                shouldIncludeTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-t border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_time_picker__WEBPACK_IMPORTED_MODULE_5__.TimePicker, {\n                                            value: (()=>{\n                                                // When confirmation is enabled, use time from pendingSelection if available\n                                                let currentTime = time;\n                                                if (confirmSelection && pendingSelection instanceof Date && !isNaN(pendingSelection.getTime())) {\n                                                    currentTime = {\n                                                        hour: pendingSelection.getHours(),\n                                                        minute: pendingSelection.getMinutes()\n                                                    };\n                                                }\n                                                if (currentTime) {\n                                                    const date = new Date();\n                                                    date.setHours(currentTime.hour, currentTime.minute, 0, 0);\n                                                    return date;\n                                                }\n                                                return undefined;\n                                            })(),\n                                            toValue: toTime ? (()=>{\n                                                const date = new Date();\n                                                date.setHours(toTime.hour, toTime.minute, 0, 0);\n                                                return date;\n                                            })() : undefined,\n                                            onChange: handleTimeChange,\n                                            onToChange: (date)=>handleTimeChange(date, true),\n                                            // Always enable time picker regardless of whether a date is selected\n                                            disabled: false,\n                                            className: \"w-full\",\n                                            mode: timeMode,\n                                            label: timeMode === \"range\" ? \"Time Range\" : undefined\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 945,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 944,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 943,\n                                    columnNumber: 33\n                                }, undefined),\n                                confirmSelection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                                            className: \"my-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 1010,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-3 flex gap-3 justify-end\"),\n                                            children: [\n                                                clearable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-fit\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: handleClearInPopover,\n                                                        iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                                        \"aria-label\": \"Clear date\",\n                                                        children: \"Clear\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                        lineNumber: 1017,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 1016,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: handleConfirm,\n                                                    disabled: !pendingSelection,\n                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(clearable ? \"\" : \"w-full\"),\n                                                    children: confirmButtonText\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 1028,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 1011,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 914,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 761,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 702,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n        lineNumber: 696,\n        columnNumber: 9\n    }, undefined);\n};\n_s(DatePicker, \"EPGvag/dmevMVfeSbcbdVrI0y2w=\");\n_c = DatePicker;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DatePicker);\nvar _c;\n$RefreshReg$(_c, \"DatePicker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0RhdGVSYW5nZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFeUI7QUFDa0I7QUFDSTtBQUNJO0FBS25CO0FBQ3NDO0FBQ2Q7QUFDbkI7QUFDb0I7QUFFUjtBQUNQO0FBZ0YxQyxNQUFNbUIsYUFBYTtRQUFDLEVBQ2hCQyxRQUFRLEVBQ1JDLFNBQVMsRUFDVEMsY0FBYyxhQUFhLEVBQzNCQyxPQUFPLE9BQU8sRUFDZEMsT0FBTyxNQUFNLEVBQ2JDLFdBQVcsS0FBSyxFQUNoQkMsS0FBSyxFQUNMQyxhQUFhLFlBQVksRUFDekJDLFVBQVUsRUFDVkMsaUJBQWlCTixTQUFTLFVBQVUsSUFBSSxDQUFDLEVBQ3pDTyxnQkFBZ0IsSUFBSSxFQUNwQkMsa0JBQWtCLEtBQUssRUFDdkJDLGNBQWMsS0FBSyxFQUNuQkMsV0FBVyxRQUFRLEVBQ25CQyxhQUFhLE9BQU8sRUFDcEJDLGVBQWUsRUFBRSxFQUNqQkMsS0FBSyxFQUNMQyxnQkFBZ0IsS0FBSyxFQUNyQkMsWUFBWSxLQUFLLEVBQ2pCQyxJQUFJLEVBQ0pDLG1CQUFtQixJQUFJLEVBQ3ZCQyxvQkFBb0IsU0FBUyxFQUM3QkMsUUFBUSxLQUFLLEVBQ2JDLG1CQUFtQixFQUFFLEVBQ3JCLEdBQUdDLGFBQ1c7O0lBQ2QsTUFBTSxDQUFDQyxXQUFXQyxhQUFhLEdBQUc3QywrQ0FBUUEsQ0FDdEN5QixTQUNLSCxDQUFBQSxTQUFTLFVBQVU7UUFBRXdCLE1BQU1DO1FBQVdDLElBQUlEO0lBQVUsSUFBSUEsU0FBUTtJQUd6RSx1R0FBdUc7SUFDdkcsTUFBTSxDQUFDRSxNQUFNQyxRQUFRLEdBQUdsRCwrQ0FBUUEsQ0FBQztJQUNqQyxNQUFNLENBQUNtRCxNQUFNQyxRQUFRLEdBQUdwRCwrQ0FBUUEsQ0FDNUI7SUFFSixNQUFNLENBQUNxRCxRQUFRQyxVQUFVLEdBQUd0RCwrQ0FBUUEsQ0FHMUI7SUFFVix1RUFBdUU7SUFDdkUsTUFBTSxDQUFDdUQsa0JBQWtCQyxvQkFBb0IsR0FBR3hELCtDQUFRQSxDQUV0RHlCLFNBQVNzQjtJQUVYLGdDQUFnQztJQUNoQyxNQUFNVSxvQkFDRm5DLFNBQVMsVUFBVSxzQkFBc0I7SUFFN0NyQixnREFBU0EsQ0FBQztRQUNOLCtEQUErRDtRQUMvRCxNQUFNeUQsVUFBVSxDQUFDQyxHQUFRQztZQUNyQixJQUFJRCxNQUFNQyxHQUFHLE9BQU87WUFFcEIsc0JBQXNCO1lBQ3RCLElBQUlELGFBQWFFLFFBQVFELGFBQWFDLE1BQU07Z0JBQ3hDLE9BQU9GLEVBQUVHLE9BQU8sT0FBT0YsRUFBRUUsT0FBTztZQUNwQztZQUVBLDJCQUEyQjtZQUMzQixJQUFJSCxLQUFLQyxLQUFLLE9BQU9ELE1BQU0sWUFBWSxPQUFPQyxNQUFNLFVBQVU7Z0JBQzFELElBQUksVUFBVUQsS0FBSyxRQUFRQSxLQUFLLFVBQVVDLEtBQUssUUFBUUEsR0FBRztvQkFDdEQsTUFBTUcsWUFDRkosRUFBRWIsSUFBSSxJQUFJYyxFQUFFZCxJQUFJLEdBQ1ZhLEVBQUViLElBQUksWUFBWWUsUUFDbEJELEVBQUVkLElBQUksWUFBWWUsUUFDbEJGLEVBQUViLElBQUksQ0FBQ2dCLE9BQU8sT0FBT0YsRUFBRWQsSUFBSSxDQUFDZ0IsT0FBTyxLQUNuQ0gsRUFBRWIsSUFBSSxLQUFLYyxFQUFFZCxJQUFJO29CQUUzQixNQUFNa0IsVUFDRkwsRUFBRVgsRUFBRSxJQUFJWSxFQUFFWixFQUFFLEdBQ05XLEVBQUVYLEVBQUUsWUFBWWEsUUFDaEJELEVBQUVaLEVBQUUsWUFBWWEsUUFDaEJGLEVBQUVYLEVBQUUsQ0FBQ2MsT0FBTyxPQUFPRixFQUFFWixFQUFFLENBQUNjLE9BQU8sS0FDL0JILEVBQUVYLEVBQUUsS0FBS1ksRUFBRVosRUFBRTtvQkFFdkIsT0FBT2UsYUFBYUM7Z0JBQ3hCO1lBQ0o7WUFFQSxPQUFPO1FBQ1g7UUFFQSxnREFBZ0Q7UUFDaEQsSUFBSSxDQUFDTixRQUFRakMsT0FBT21CLFlBQVk7WUFDNUIsSUFBSW5CLE9BQU87Z0JBQ1BvQixhQUFhcEI7Z0JBQ2IsOENBQThDO2dCQUM5QytCLG9CQUFvQi9CO2dCQUVwQixJQUFJQSxpQkFBaUJvQyxNQUFNO29CQUN2QixNQUFNSSxRQUFReEMsTUFBTXlDLFFBQVE7b0JBQzVCLE1BQU1DLFVBQVUxQyxNQUFNMkMsVUFBVTtvQkFDaEMsSUFBSUgsU0FBU0UsU0FDVGYsUUFBUTt3QkFBRWlCLE1BQU1KO3dCQUFPSyxRQUFRSDtvQkFBUTtnQkFDL0MsT0FBTyxJQUFJLFVBQVUxQyxTQUFTQSxNQUFNcUIsSUFBSSxZQUFZZSxNQUFNO29CQUN0RCxNQUFNLEVBQUVmLElBQUksRUFBRUUsRUFBRSxFQUFFLEdBQUd2QjtvQkFDckIsTUFBTThDLFlBQVl6QixLQUFLb0IsUUFBUTtvQkFDL0IsTUFBTU0sY0FBYzFCLEtBQUtzQixVQUFVO29CQUNuQyxJQUFJRyxhQUFhQyxhQUNicEIsUUFBUTt3QkFBRWlCLE1BQU1FO3dCQUFXRCxRQUFRRTtvQkFBWTtvQkFDbkQsSUFBSXhCLGNBQWNhLE1BQU07d0JBQ3BCLE1BQU1ZLFVBQVV6QixHQUFHa0IsUUFBUTt3QkFDM0IsTUFBTVEsWUFBWTFCLEdBQUdvQixVQUFVO3dCQUMvQixJQUFJSyxXQUFXQyxXQUNYcEIsVUFBVTs0QkFBRWUsTUFBTUk7NEJBQVNILFFBQVFJO3dCQUFVO29CQUNyRDtnQkFDSjtZQUNKLE9BQU87Z0JBQ0gsMkVBQTJFO2dCQUMzRTdCLGFBQ0l2QixTQUFTLFVBQ0g7b0JBQUV3QixNQUFNQztvQkFBV0MsSUFBSUQ7Z0JBQVUsSUFDakNBO2dCQUVWLDhCQUE4QjtnQkFDOUJTLG9CQUFvQlQ7WUFDeEI7UUFDSjtJQUNKLEdBQUc7UUFBQ3RCO1FBQU9IO0tBQUs7SUFFaEIsTUFBTXFELGVBQWUsQ0FBQ0M7UUFDbEIsSUFBSSxDQUFDakQsWUFBWSxPQUFPO1FBQ3hCLE1BQU0sRUFBRWtELE9BQU8sRUFBRUMsT0FBTyxFQUFFQyxhQUFhLEVBQUVDLGtCQUFrQixFQUFFLEdBQ3pEckQ7UUFDSixJQUFJa0QsV0FBVy9ELHFHQUFRQSxDQUFDOEQsTUFBTUMsVUFBVSxPQUFPO1FBQy9DLElBQUlDLFdBQVdqRSxxR0FBT0EsQ0FBQytELE1BQU1FLFVBQVUsT0FBTztRQUM5QyxJQUNJQywwQkFBQUEsb0NBQUFBLGNBQWVFLElBQUksQ0FDZixDQUFDQyxlQUNHdEUsb0dBQU1BLENBQUNzRSxjQUFjLGtCQUNyQnRFLG9HQUFNQSxDQUFDZ0UsTUFBTSxnQkFHckIsT0FBTztRQUNYLElBQUlJLCtCQUFBQSx5Q0FBQUEsbUJBQW9CRyxRQUFRLENBQUNQLEtBQUtRLE1BQU0sS0FBSyxPQUFPO1FBQ3hELE9BQU87SUFDWDtJQUVBLE1BQU1DLFlBQVksQ0FDZFQsTUFDQVUsSUFFQVYsUUFBUVUsSUFDRnZFLGlHQUFHQSxDQUFDNkQsTUFBTTtZQUNOWCxPQUFPcUIsRUFBRWpCLElBQUk7WUFDYkYsU0FBU21CLEVBQUVoQixNQUFNO1lBQ2pCaUIsU0FBUztZQUNUQyxjQUFjO1FBQ2xCLEtBQ0FaO0lBRVYsTUFBTWEsb0JBQW9CLENBQUNDO1FBQ3ZCLElBQUksQ0FBQ0EsVUFBVTtZQUNYLDZEQUE2RDtZQUM3RDdDLGFBQ0l2QixTQUFTLFVBQ0g7Z0JBQUV3QixNQUFNQztnQkFBV0MsSUFBSUQ7WUFBVSxJQUNqQ0E7WUFFVlMsb0JBQW9CVDtZQUNwQjVCLFNBQVM7WUFDVDtRQUNKO1FBRUEsSUFBSUcsU0FBUyxTQUFTO1lBQ2xCLE1BQU0sRUFBRXdCLElBQUksRUFBRUUsRUFBRSxFQUFFLEdBQUcwQztZQUNyQixJQUFJNUMsUUFBUSxDQUFDNkIsYUFBYTdCLE9BQU87WUFDakMsSUFBSUUsTUFBTSxDQUFDMkIsYUFBYTNCLEtBQUs7WUFFN0Isb0VBQW9FO1lBQ3BFLElBQUlULGtCQUFrQjtnQkFDbEJpQixvQkFBb0I7b0JBQUVWO29CQUFNRTtnQkFBRztZQUNuQyxPQUFPO2dCQUNISCxhQUFhO29CQUFFQztvQkFBTUU7Z0JBQUc7Z0JBRXhCLHNEQUFzRDtnQkFDdEQsSUFBSTJDLGNBQWN4QztnQkFDbEIsSUFBSSxDQUFDd0MsZUFBZUMsbUJBQW1CO29CQUNuQyxNQUFNQyxNQUFNLElBQUloQztvQkFDaEI4QixjQUFjO3dCQUNWdEIsTUFBTXdCLElBQUkzQixRQUFRO3dCQUNsQkksUUFBUXVCLElBQUl6QixVQUFVO29CQUMxQjtvQkFDQWhCLFFBQVF1QztnQkFDWjtnQkFFQSxJQUFJRyxnQkFBZ0J6QztnQkFDcEIsSUFBSSxDQUFDeUMsaUJBQWlCRixxQkFBcUI1QyxJQUFJO29CQUMzQyxNQUFNNkMsTUFBTSxJQUFJaEM7b0JBQ2hCaUMsZ0JBQWdCO3dCQUNaekIsTUFBTXdCLElBQUkzQixRQUFRO3dCQUNsQkksUUFBUXVCLElBQUl6QixVQUFVO29CQUMxQjtvQkFDQWQsVUFBVXdDO2dCQUNkO2dCQUVBM0UsU0FBUztvQkFDTDRFLFdBQVdWLFVBQVV2QyxNQUFNNkM7b0JBQzNCSyxTQUFTWCxVQUFVckMsSUFBSThDO2dCQUMzQjtZQUNKO1FBQ0osT0FBTztZQUNILE1BQU1HLGFBQWFQO1lBQ25CLElBQUksQ0FBQ2YsYUFBYXNCLGFBQWE7WUFFL0Isb0VBQW9FO1lBQ3BFLElBQUkxRCxrQkFBa0I7Z0JBQ2xCaUIsb0JBQW9CeUM7WUFDeEIsT0FBTztnQkFDSHBELGFBQWFvRDtnQkFFYixzRkFBc0Y7Z0JBQ3RGLElBQUlOLGNBQWN4QztnQkFFbEIsaUVBQWlFO2dCQUNqRSxJQUNJLENBQUN3QyxlQUNEQyxxQkFDQWhELHFCQUFxQmlCLE1BQ3ZCO29CQUNFOEIsY0FBYzt3QkFDVnRCLE1BQU16QixVQUFVc0IsUUFBUTt3QkFDeEJJLFFBQVExQixVQUFVd0IsVUFBVTtvQkFDaEM7b0JBQ0FoQixRQUFRdUM7Z0JBQ1osT0FBTyxJQUFJLENBQUNBLGVBQWVDLG1CQUFtQjtvQkFDMUMseURBQXlEO29CQUN6RCxNQUFNQyxNQUFNLElBQUloQztvQkFDaEI4QixjQUFjO3dCQUNWdEIsTUFBTXdCLElBQUkzQixRQUFRO3dCQUNsQkksUUFBUXVCLElBQUl6QixVQUFVO29CQUMxQjtvQkFDQWhCLFFBQVF1QztnQkFDWjtnQkFFQSxpREFBaUQ7Z0JBQ2pELE1BQU1PLFNBQVNOLG9CQUNUUCxVQUFVWSxZQUFZTixlQUN0Qk07Z0JBRU45RSxTQUFTK0U7Z0JBRVQsSUFBSXJFLGlCQUFpQixDQUFDK0QsbUJBQW1CMUMsUUFBUTtZQUNyRDtRQUNKO0lBQ0o7SUFFQSxNQUFNaUQsbUJBQW1CLFNBQUN2QjtZQUFZd0IsNEVBQVc7UUFDN0MsSUFBSSxDQUFDeEIsTUFBTTtRQUVYLE1BQU15QixVQUFVO1lBQUVoQyxNQUFNTyxLQUFLVixRQUFRO1lBQUlJLFFBQVFNLEtBQUtSLFVBQVU7UUFBRztRQUVuRSwrREFBK0Q7UUFDL0QsSUFBSWdDLFVBQVU7WUFDVixNQUFNTixnQkFBZ0J6QztZQUV0QixJQUNJLENBQUN5QyxpQkFDREEsY0FBY3pCLElBQUksS0FBS2dDLFFBQVFoQyxJQUFJLElBQ25DeUIsY0FBY3hCLE1BQU0sS0FBSytCLFFBQVEvQixNQUFNLEVBQ3pDO2dCQUNFaEIsVUFBVStDO2dCQUVWLDRDQUE0QztnQkFDNUMsSUFBSTlELHFCQUFxQmdCLDZCQUFBQSx1Q0FBRCxpQkFBaUNQLEVBQUUsR0FBRTtvQkFDekQsTUFBTXNELGFBQWF2RixpR0FBR0EsQ0FDbEIsaUJBQWdDaUMsRUFBRSxFQUNsQzt3QkFDSWlCLE9BQU9vQyxRQUFRaEMsSUFBSTt3QkFDbkJGLFNBQVNrQyxRQUFRL0IsTUFBTTt3QkFDdkJpQixTQUFTO3dCQUNUQyxjQUFjO29CQUNsQjtvQkFHSmhDLG9CQUFvQjt3QkFDaEIsR0FBSUQsZ0JBQWdCO3dCQUNwQlAsSUFBSXNEO29CQUNSO2dCQUNKO2dCQUVBLDBEQUEwRDtnQkFDMUQsSUFBSSxDQUFDL0Qsa0JBQWtCO29CQUNuQixJQUFLSyxzQkFBQUEsZ0NBQUQsVUFBMEJJLEVBQUUsRUFBRTt3QkFDOUIsTUFBTXNELGFBQWF2RixpR0FBR0EsQ0FBQyxVQUF5QmlDLEVBQUUsRUFBRzs0QkFDakRpQixPQUFPb0MsUUFBUWhDLElBQUk7NEJBQ25CRixTQUFTa0MsUUFBUS9CLE1BQU07NEJBQ3ZCaUIsU0FBUzs0QkFDVEMsY0FBYzt3QkFDbEI7d0JBRUFyRSxTQUFTOzRCQUNMNEUsV0FBVyxVQUF5QmpELElBQUk7NEJBQ3hDa0QsU0FBU007d0JBQ2I7b0JBQ0o7Z0JBQ0o7WUFDSjtRQUNKLE9BQU87WUFDSCxNQUFNWCxjQUFjeEM7WUFFcEIsSUFDSSxDQUFDd0MsZUFDREEsWUFBWXRCLElBQUksS0FBS2dDLFFBQVFoQyxJQUFJLElBQ2pDc0IsWUFBWXJCLE1BQU0sS0FBSytCLFFBQVEvQixNQUFNLEVBQ3ZDO2dCQUNFbEIsUUFBUWlEO2dCQUVSLDRDQUE0QztnQkFDNUMsSUFBSTlELGtCQUFrQjtvQkFDbEIsSUFBS2dCLDZCQUFBQSx1Q0FBRCxpQkFBaUNULElBQUksRUFBRTt3QkFDdkMsTUFBTXlELGVBQWV4RixpR0FBR0EsQ0FDcEIsaUJBQWdDK0IsSUFBSSxFQUNwQzs0QkFDSW1CLE9BQU9vQyxRQUFRaEMsSUFBSTs0QkFDbkJGLFNBQVNrQyxRQUFRL0IsTUFBTTs0QkFDdkJpQixTQUFTOzRCQUNUQyxjQUFjO3dCQUNsQjt3QkFHSmhDLG9CQUFvQjs0QkFDaEIsR0FBSUQsZ0JBQWdCOzRCQUNwQlQsTUFBTXlEO3dCQUNWO29CQUNKLE9BQU8sSUFBSWhELDRCQUE0Qk0sTUFBTTt3QkFDekMsTUFBTTJDLFVBQVV6RixpR0FBR0EsQ0FBQ3dDLGtCQUEwQjs0QkFDMUNVLE9BQU9vQyxRQUFRaEMsSUFBSTs0QkFDbkJGLFNBQVNrQyxRQUFRL0IsTUFBTTs0QkFDdkJpQixTQUFTOzRCQUNUQyxjQUFjO3dCQUNsQjt3QkFFQWhDLG9CQUFvQmdEO29CQUN4QjtnQkFDSjtnQkFFQSwwREFBMEQ7Z0JBQzFELElBQUksQ0FBQ2pFLGtCQUFrQjtvQkFDbkIsSUFBS0ssc0JBQUFBLGdDQUFELFVBQTBCRSxJQUFJLEVBQUU7d0JBQ2hDLE1BQU15RCxlQUFleEYsaUdBQUdBLENBQ3BCLFVBQXlCK0IsSUFBSSxFQUM3Qjs0QkFDSW1CLE9BQU9vQyxRQUFRaEMsSUFBSTs0QkFDbkJGLFNBQVNrQyxRQUFRL0IsTUFBTTs0QkFDdkJpQixTQUFTOzRCQUNUQyxjQUFjO3dCQUNsQjt3QkFHSnJFLFNBQVM7NEJBQ0w0RSxXQUFXUTs0QkFDWFAsU0FBUyxVQUF5QmhELEVBQUU7d0JBQ3hDO29CQUNKLE9BQU8sSUFBSUoscUJBQXFCaUIsTUFBTTt3QkFDbEMsTUFBTTJDLFVBQVV6RixpR0FBR0EsQ0FBQzZCLFdBQVc7NEJBQzNCcUIsT0FBT29DLFFBQVFoQyxJQUFJOzRCQUNuQkYsU0FBU2tDLFFBQVEvQixNQUFNOzRCQUN2QmlCLFNBQVM7NEJBQ1RDLGNBQWM7d0JBQ2xCO3dCQUVBckUsU0FBU3FGO29CQUNiO2dCQUNKO1lBQ0o7UUFDSjtJQUNKO0lBRUEsTUFBTUMsY0FBYyxDQUFDQztRQUNqQkEsRUFBRUMsZUFBZSxHQUFHLGlDQUFpQzs7UUFDckQ5RCxhQUNJdkIsU0FBUyxVQUFVO1lBQUV3QixNQUFNQztZQUFXQyxJQUFJRDtRQUFVLElBQUlBO1FBRTVEUyxvQkFBb0JUO1FBQ3BCSyxRQUFRO1FBQ1JFLFVBQVU7UUFDVm5DLFNBQVM7SUFDYjtJQUVBLDJEQUEyRDtJQUMzRCxNQUFNeUYsdUJBQXVCO1FBQ3pCL0QsYUFDSXZCLFNBQVMsVUFBVTtZQUFFd0IsTUFBTUM7WUFBV0MsSUFBSUQ7UUFBVSxJQUFJQTtRQUU1RFMsb0JBQW9CVDtRQUNwQkssUUFBUTtRQUNSRSxVQUFVO1FBQ1ZuQyxTQUFTO1FBQ1QsbUNBQW1DO1FBQ25DK0IsUUFBUTtJQUNaO0lBRUEsK0NBQStDO0lBQy9DLE1BQU0yRCxnQkFBZ0I7UUFDbEIsSUFBSSxDQUFDdEQsa0JBQWtCO1FBRXZCLElBQUlqQyxTQUFTLFNBQVM7WUFDbEIsTUFBTSxFQUFFd0IsSUFBSSxFQUFFRSxFQUFFLEVBQUUsR0FBR087WUFDckJWLGFBQWE7Z0JBQUVDO2dCQUFNRTtZQUFHO1lBRXhCLHVCQUF1QjtZQUN2QixJQUFJMkMsY0FBY3hDO1lBQ2xCLElBQUksQ0FBQ3dDLGVBQWVDLHFCQUFxQjlDLE1BQU07Z0JBQzNDLE1BQU0rQyxNQUFNLElBQUloQztnQkFDaEI4QixjQUFjO29CQUFFdEIsTUFBTXdCLElBQUkzQixRQUFRO29CQUFJSSxRQUFRdUIsSUFBSXpCLFVBQVU7Z0JBQUc7Z0JBQy9EaEIsUUFBUXVDO1lBQ1o7WUFFQSxJQUFJRyxnQkFBZ0J6QztZQUNwQixJQUFJLENBQUN5QyxpQkFBaUJGLHFCQUFxQjVDLElBQUk7Z0JBQzNDLE1BQU02QyxNQUFNLElBQUloQztnQkFDaEJpQyxnQkFBZ0I7b0JBQ1p6QixNQUFNd0IsSUFBSTNCLFFBQVE7b0JBQ2xCSSxRQUFRdUIsSUFBSXpCLFVBQVU7Z0JBQzFCO2dCQUNBZCxVQUFVd0M7WUFDZDtZQUVBM0UsU0FBUztnQkFDTDRFLFdBQVdWLFVBQVV2QyxNQUFNNkM7Z0JBQzNCSyxTQUFTWCxVQUFVckMsSUFBSThDO1lBQzNCO1FBQ0osT0FBTztZQUNILE1BQU1HLGFBQWExQztZQUNuQlYsYUFBYW9EO1lBRWIsdUJBQXVCO1lBQ3ZCLElBQUlOLGNBQWN4QztZQUVsQixpRUFBaUU7WUFDakUsSUFDSSxDQUFDd0MsZUFDREMscUJBQ0FoRCxxQkFBcUJpQixNQUN2QjtnQkFDRThCLGNBQWM7b0JBQ1Z0QixNQUFNekIsVUFBVXNCLFFBQVE7b0JBQ3hCSSxRQUFRMUIsVUFBVXdCLFVBQVU7Z0JBQ2hDO2dCQUNBaEIsUUFBUXVDO1lBQ1osT0FBTyxJQUFJLENBQUNBLGVBQWVDLG1CQUFtQjtnQkFDMUMseURBQXlEO2dCQUN6RCxNQUFNQyxNQUFNLElBQUloQztnQkFDaEI4QixjQUFjO29CQUFFdEIsTUFBTXdCLElBQUkzQixRQUFRO29CQUFJSSxRQUFRdUIsSUFBSXpCLFVBQVU7Z0JBQUc7Z0JBQy9EaEIsUUFBUXVDO1lBQ1o7WUFFQSxNQUFNTyxTQUFTTixvQkFDVFAsVUFBVVksWUFBWU4sZUFDdEJNO1lBRU45RSxTQUFTK0U7UUFDYjtRQUVBLHVDQUF1QztRQUN2Q2hELFFBQVE7UUFDUiwwQkFBMEI7UUFDMUJNLG9CQUFvQlQ7SUFDeEI7SUFFQSw4REFBOEQ7SUFFOUQsZ0dBQWdHO0lBQ2hHLE1BQU0sQ0FBQzZDLG1CQUFtQmtCLHFCQUFxQixHQUFHOUcsK0NBQVFBLENBQ3REdUIsU0FBUyxjQUFjUTtJQUczQiw0REFBNEQ7SUFDNUQ5QixnREFBU0EsQ0FBQztRQUNONkcscUJBQXFCdkYsU0FBUyxjQUFjUTtJQUNoRCxHQUFHO1FBQUNSO1FBQU1RO0tBQVk7SUFFdEIsTUFBTWdGLG9CQUFvQm5CLG9CQUNwQixHQUFpQjNELE9BQWRQLFlBQVcsS0FBYyxPQUFYTyxjQUNqQlA7SUFFTiw4QkFBOEI7SUFDOUIsTUFBTXNGLHFCQUFxQixDQUFDcEM7UUFDeEIsSUFBSSxDQUFDQSxNQUFNLE9BQU87UUFDbEIsTUFBTXFDLFlBQVlyQyxnQkFBZ0JmLE9BQU9lLE9BQU8sSUFBSWY7UUFDcEQsT0FBT3FELE1BQU1ELFVBQVVuRCxPQUFPLE1BQ3hCLEtBQ0FsRCxvR0FBTUEsQ0FBQ3FHLFdBQVdGO0lBQzVCO0lBRUEsZ0ZBQWdGO0lBRWhGLElBQUl2RixVQUFVO1FBQ1Ysa0VBQWtFO1FBQ2xFLE1BQU0yRixjQUFjLENBQUN2QztZQUNqQixJQUFJLENBQUNBLFFBQVEsQ0FBRUEsQ0FBQUEsZ0JBQWdCZixJQUFHLEtBQU1xRCxNQUFNdEMsS0FBS2QsT0FBTyxLQUFLO2dCQUMzRCxPQUFPbEQsb0dBQU1BLENBQUMsSUFBSWlELFFBQVFuQztZQUM5QjtZQUNBLE9BQU9kLG9HQUFNQSxDQUNUZ0UsTUFDQWdCLG9CQUFvQm1CLG9CQUFvQnJGO1FBRWhEO1FBRUEsOENBQThDO1FBQzlDLElBQUkwRjtRQUNKLElBQUk5RixTQUFTLFNBQVM7WUFDbEIsTUFBTStGLFFBQVF6RTtZQUNkLElBQUl5RSxrQkFBQUEsNEJBQUFBLE1BQU92RSxJQUFJLEVBQUU7Z0JBQ2JzRSxlQUFlQyxNQUFNckUsRUFBRSxHQUNqQixHQUFnQ21FLE9BQTdCQSxZQUFZRSxNQUFNdkUsSUFBSSxHQUFFLE9BQTJCLE9BQXRCcUUsWUFBWUUsTUFBTXJFLEVBQUUsS0FDcERtRSxZQUFZRSxNQUFNdkUsSUFBSTtZQUNoQyxPQUFPO2dCQUNILE1BQU13RSxtQkFBbUIxRyxvR0FBTUEsQ0FBQyxJQUFJaUQsUUFBUW5DO2dCQUM1QzBGLGVBQWUsR0FBeUJFLE9BQXRCQSxrQkFBaUIsT0FBc0IsT0FBakJBO1lBQzVDO1FBQ0osT0FBTztZQUNIRixlQUNJeEUscUJBQXFCaUIsT0FDZnNELFlBQVl2RSxhQUNaaEMsb0dBQU1BLENBQUMsSUFBSWlELFFBQVFuQztRQUNqQztRQUVBLHFCQUNJLDhEQUFDNkY7O2dCQUNJcEYsdUJBQ0csOERBQUNuQiw0Q0FBS0E7b0JBQ0Z3RyxPQUFPO29CQUNQQyxJQUFHO29CQUNIQyxVQUFVdEY7b0JBQ1ZaLFVBQVVBOzhCQUNUVzs7Ozs7OzhCQUdULDhEQUFDb0Y7b0JBQUluRyxXQUFVOzhCQUNYLDRFQUFDbEIseURBQU1BO3dCQUNIdUgsSUFBRzt3QkFDSEUsU0FBUTt3QkFDUm5HLFVBQVVBO3dCQUNWSixXQUFXVCxrREFBRUEsQ0FBQzt3QkFDZGlILFVBQ0l0RixxQkFDSXZDLDJEQUFvQixDQUFDdUMsc0JBQ2pCdkMseURBQWtCLENBQ2R1QyxNQUNBOzRCQUNJbEIsV0FDSTt3QkFDUixtQkFHSnJCLDBEQUFtQixDQUFDdUMsTUFBYTs0QkFDN0IwRixNQUFNOzRCQUNONUcsV0FDSTt3QkFDUixtQkFHSiw4REFBQ2IsaUdBQVlBOzRCQUFDYSxXQUFVOzs7Ozs7d0JBRy9CLEdBQUd1QixXQUFXO3dCQUNmcEIsTUFBSztrQ0FDSjZGOzs7Ozs7Ozs7Ozs7Ozs7OztJQUtyQjtJQUVBLE1BQU1hLG9CQUFvQjtRQUN0QixJQUFJM0csU0FBUyxTQUFTO1lBQ2xCLE1BQU0rRixRQUFRekU7WUFDZCxJQUFJeUUsa0JBQUFBLDRCQUFBQSxNQUFPdkUsSUFBSSxFQUFFO2dCQUNiLE9BQU91RSxNQUFNckUsRUFBRSxpQkFDWDs7d0JBQ0tnRSxtQkFBbUJLLE1BQU12RSxJQUFJO3dCQUFFO3dCQUFHO3dCQUNsQ2tFLG1CQUFtQkssTUFBTXJFLEVBQUU7O21DQUdoQ2dFLG1CQUFtQkssTUFBTXZFLElBQUk7WUFFckM7WUFDQSw2Q0FBNkM7WUFDN0MscUJBQ0ksOERBQUNvRjtnQkFBSzlHLFdBQVU7MEJBQ1hxQzs7Ozs7O1FBR2IsT0FBTyxJQUFJYixXQUFXO1lBQ2xCLE9BQU9vRSxtQkFBbUJwRTtRQUM5QjtRQUNBLDZDQUE2QztRQUM3QyxxQkFDSSw4REFBQ3NGO1lBQUs5RyxXQUFVO3NCQUF5QnFDOzs7Ozs7SUFFakQ7SUFFQSxxQkFDSSw4REFBQzhEO1FBQUluRyxXQUFXVCxrREFBRUEsQ0FBQytCOztZQUNkUCx1QkFDRyw4REFBQ25CLDRDQUFLQTtnQkFBQ3lHLElBQUc7Z0JBQU9DLFVBQVV0RjtnQkFBZVosVUFBVUE7MEJBQy9DVzs7Ozs7OzBCQUdULDhEQUFDL0IsMkRBQU9BO2dCQUNKcUMsT0FBT0E7Z0JBQ1BRLE1BQU1BO2dCQUNOa0YsY0FBYyxDQUFDQztvQkFDWCwrRUFBK0U7b0JBQy9FLElBQUlBLFVBQVUsQ0FBQzdFLG9CQUFvQlgsV0FBVzt3QkFDMUNZLG9CQUFvQlo7b0JBQ3hCO29CQUNBTSxRQUFRa0Y7Z0JBQ1o7O2tDQUNBLDhEQUFDOUgsa0VBQWNBO3dCQUFDa0gsT0FBTztrQ0FDbkIsNEVBQUNEOzRCQUFJbkcsV0FBVTs7OENBQ1gsOERBQUNsQix5REFBTUE7b0NBQ0h1SCxJQUFHO29DQUNIRSxTQUFRO29DQUNSbkcsVUFBVUE7b0NBQ1ZvRyxVQUNJdEYscUJBQ0l2QywyREFBb0IsQ0FBQ3VDLFFBQ2pCQSxxQkFFQXZDLDBEQUFtQixDQUFDdUMsTUFBYTt3Q0FDN0IwRixNQUFNO3dDQUNONUcsV0FBVztvQ0FDZixtQkFHSiw4REFBQ2IsaUdBQVlBO3dDQUNUeUgsTUFBTTt3Q0FDTjVHLFdBQVU7Ozs7OztvQ0FJdEJBLFdBQVdULGtEQUFFQSxDQUNULDZCQUNBMEIsYUFBYTtvQ0FFaEIsR0FBR00sV0FBVztvQ0FDZnBCLE1BQUs7OENBQ0owRzs7Ozs7O2dDQUVKNUYsYUFDSU8sQ0FBQUEscUJBQXFCaUIsU0FDakJqQixzQkFBQUEsZ0NBQUQsVUFBMEJFLElBQUksQ0FBRCxtQkFDN0IsOERBQUM1Qyx5REFBTUE7b0NBQ0hxQixNQUFLO29DQUNMb0csU0FBUTtvQ0FDUkssTUFBSztvQ0FDTDVHLFdBQVU7b0NBQ1ZpSCxTQUFTNUI7b0NBQ1Q2QixjQUFXOzhDQUNYLDRFQUFDOUgsaUdBQUNBO3dDQUNFd0gsTUFBTTt3Q0FDTjVHLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTWxDLDhEQUFDZixrRUFBY0E7d0JBQUNlLFdBQVU7d0JBQWFtSCxPQUFNO2tDQUN4Q2pILFNBQVMsd0JBQ04sOERBQUNpRzs7OENBQ0csOERBQUNwSCw2REFBUUE7b0NBRUxxSSxTQUFTO29DQUNUbEgsTUFBSztvQ0FDTG1ILE9BQ0ksQ0FBQ2xHLG9CQUFvQmdCLG1CQUNkQSw2QkFBQUEsdUNBQUQsaUJBQWlDVCxJQUFJLEdBQ3BDRixzQkFBQUEsZ0NBQUQsVUFBMEJFLElBQUksS0FDcEMsSUFBSWU7b0NBRVIsc0NBQXNDO29DQUN0QyxrQ0FBa0M7b0NBQ2xDNkUsZUFBYztvQ0FDZEMsVUFDSXBHLG9CQUFvQmdCLG1CQUNiQSxtQkFDQVg7b0NBRVhnRyxVQUFVLENBQUNuSDt3Q0FDUCwrREFBK0Q7d0NBQy9EZ0Usa0JBQWtCaEU7b0NBQ3RCO29DQUNBRyxnQkFBZ0JBO29DQUNoQmlILGdCQUFnQi9HO29DQUNoQk4sVUFDSUcsYUFDTSxDQUFDaUQsT0FBUyxDQUFDRCxhQUFhQyxRQUN4QjdCO21DQTFCTjs7Ozs7Z0NBNkJQNkMsbUNBQ0csOERBQUMyQjtvQ0FBSW5HLFdBQVU7OENBQ1gsNEVBQUNtRzt3Q0FBSW5HLFdBQVU7a0RBQ1gsNEVBQUNWLGtFQUFVQTs0Q0FDUGUsT0FBTyxDQUFDO2dEQUNKLDRFQUE0RTtnREFDNUUsSUFBSWtFLGNBQWN4QztnREFDbEIsSUFDSVoscUJBRUlnQiw2QkFBQUEsdUNBREosaUJBRUdULElBQUksR0FDVDtvREFDRSxNQUFNZ0csV0FBVyxpQkFFZmhHLElBQUk7b0RBQ042QyxjQUFjO3dEQUNWdEIsTUFBTXlFLFNBQVM1RSxRQUFRO3dEQUN2QkksUUFBUXdFLFNBQVMxRSxVQUFVO29EQUMvQjtnREFDSjtnREFFQSxJQUFJdUIsYUFBYTtvREFDYixNQUFNZixPQUFPLElBQUlmO29EQUNqQmUsS0FBS21FLFFBQVEsQ0FDVHBELFlBQVl0QixJQUFJLEVBQ2hCc0IsWUFBWXJCLE1BQU0sRUFDbEIsR0FDQTtvREFFSixPQUFPTTtnREFDWDtnREFDQSxxREFBcUQ7Z0RBQ3JELE1BQU1pQixNQUFNLElBQUloQztnREFDaEIsTUFBTXdDLFVBQVU7b0RBQ1poQyxNQUFNd0IsSUFBSTNCLFFBQVE7b0RBQ2xCSSxRQUFRdUIsSUFBSXpCLFVBQVU7Z0RBQzFCO2dEQUNBaEIsUUFBUWlEO2dEQUNSLE9BQU9SOzRDQUNYOzRDQUNBbUQsU0FBUyxDQUFDO2dEQUNOLDRFQUE0RTtnREFDNUUsSUFBSWxELGdCQUFnQnpDO2dEQUNwQixJQUNJZCxxQkFFSWdCLDZCQUFBQSx1Q0FESixpQkFFR1AsRUFBRSxHQUNQO29EQUNFLE1BQU1pRyxTQUFTLGlCQUViakcsRUFBRTtvREFDSjhDLGdCQUFnQjt3REFDWnpCLE1BQU00RSxPQUFPL0UsUUFBUTt3REFDckJJLFFBQVEyRSxPQUFPN0UsVUFBVTtvREFDN0I7Z0RBQ0o7Z0RBRUEsSUFBSTBCLGVBQWU7b0RBQ2YsTUFBTWxCLE9BQU8sSUFBSWY7b0RBQ2pCZSxLQUFLbUUsUUFBUSxDQUNUakQsY0FBY3pCLElBQUksRUFDbEJ5QixjQUFjeEIsTUFBTSxFQUNwQixHQUNBO29EQUVKLE9BQU9NO2dEQUNYO2dEQUNBLE9BQU83Qjs0Q0FDWDs0Q0FDQTVCLFVBQVVnRjs0Q0FDVitDLFlBQVksQ0FBQ3RFLE9BQ1R1QixpQkFBaUJ2QixNQUFNOzRDQUUzQixxRUFBcUU7NENBQ3JFcEQsVUFBVTs0Q0FDVkosV0FBVTs0Q0FDVkUsTUFBTVU7NENBQ05HLE9BQ0lILGFBQWEsVUFDUCxlQUNBZTs7Ozs7Ozs7Ozs7Ozs7OztnQ0FNekJSLGtDQUNHOztzREFDSSw4REFBQ3RCLG9EQUFTQTs0Q0FBQ0csV0FBVTs7Ozs7O3NEQUNyQiw4REFBQ21HOzRDQUNHbkcsV0FBV1Qsa0RBQUVBLENBQ1Q7O2dEQUVIMEIsMkJBQ0csOERBQUNrRjtvREFBSW5HLFdBQVU7OERBQ1gsNEVBQUNsQix5REFBTUE7d0RBQ0h5SCxTQUFRO3dEQUNSVSxTQUNJekI7d0RBRUpnQixVQUFVcEgsaUdBQUNBO3dEQUNYWSxXQUFVO3dEQUNWa0gsY0FBVztrRUFBbUI7Ozs7Ozs7Ozs7OzhEQUsxQyw4REFBQ3BJLHlEQUFNQTtvREFDSG1JLFNBQVN4QjtvREFDVHJGLFVBQVUsQ0FBQytCO29EQUNYcUUsVUFBVW5ILGlHQUFLQTs4REFDZCtCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQU9yQiw4REFBQytFOzs4Q0FDRyw4REFBQ3BILDZEQUFRQTtvQ0FFTHFJLFNBQVM7b0NBQ1RsSCxNQUFLO29DQUNMbUgsT0FDSSxDQUFDbEcsb0JBQW9CZ0IsbUJBQ2RBLG1CQUNBWCxTQUFpQixLQUFNLElBQUlpQjtvQ0FFdEM4RSxVQUNJcEcsb0JBQW9CZ0IsbUJBQ2JBLG1CQUNBWDtvQ0FFWGdHLFVBQVUsQ0FBQ25IO3dDQUNQLCtEQUErRDt3Q0FDL0RnRSxrQkFBa0JoRTtvQ0FDdEI7b0NBQ0FpSCxlQUFjO29DQUNkOUcsZ0JBQWdCQTtvQ0FDaEJpSCxnQkFBZ0IvRztvQ0FDaEJOLFVBQ0lHLGFBQ00sQ0FBQ2lELE9BQVMsQ0FBQ0QsYUFBYUMsUUFDeEI3QjttQ0F2Qk47Ozs7O2dDQTBCUDZDLG1DQUNHLDhEQUFDMkI7b0NBQUluRyxXQUFVOzhDQUNYLDRFQUFDbUc7d0NBQUluRyxXQUFVO2tEQUNYLDRFQUFDVixrRUFBVUE7NENBQ1BlLE9BQU8sQ0FBQztnREFDSiw0RUFBNEU7Z0RBQzVFLElBQUlrRSxjQUFjeEM7Z0RBRWxCLElBQ0laLG9CQUNBZ0IsNEJBQ0lNLFFBQ0osQ0FBQ3FELE1BQ0czRCxpQkFBaUJPLE9BQU8sS0FFOUI7b0RBQ0U2QixjQUFjO3dEQUNWdEIsTUFBTWQsaUJBQWlCVyxRQUFRO3dEQUMvQkksUUFBUWYsaUJBQWlCYSxVQUFVO29EQUN2QztnREFDSjtnREFFQSxJQUFJdUIsYUFBYTtvREFDYixNQUFNZixPQUFPLElBQUlmO29EQUNqQmUsS0FBS21FLFFBQVEsQ0FDVHBELFlBQVl0QixJQUFJLEVBQ2hCc0IsWUFBWXJCLE1BQU0sRUFDbEIsR0FDQTtvREFFSixPQUFPTTtnREFDWDtnREFDQSxPQUFPN0I7NENBQ1g7NENBQ0FpRyxTQUNJM0YsU0FDTSxDQUFDO2dEQUNHLE1BQU11QixPQUNGLElBQUlmO2dEQUNSZSxLQUFLbUUsUUFBUSxDQUNUMUYsT0FBT2dCLElBQUksRUFDWGhCLE9BQU9pQixNQUFNLEVBQ2IsR0FDQTtnREFFSixPQUFPTTs0Q0FDWCxPQUNBN0I7NENBRVY1QixVQUFVZ0Y7NENBQ1YrQyxZQUFZLENBQUN0RSxPQUNUdUIsaUJBQWlCdkIsTUFBTTs0Q0FFM0IscUVBQXFFOzRDQUNyRXBELFVBQVU7NENBQ1ZKLFdBQVU7NENBQ1ZFLE1BQU1VOzRDQUNORyxPQUNJSCxhQUFhLFVBQ1AsZUFDQWU7Ozs7Ozs7Ozs7Ozs7Ozs7Z0NBTXpCUixrQ0FDRzs7c0RBQ0ksOERBQUN0QixvREFBU0E7NENBQUNHLFdBQVU7Ozs7OztzREFDckIsOERBQUNtRzs0Q0FDR25HLFdBQVdULGtEQUFFQSxDQUNUOztnREFFSDBCLDJCQUNHLDhEQUFDa0Y7b0RBQUluRyxXQUFVOzhEQUNYLDRFQUFDbEIseURBQU1BO3dEQUNIeUgsU0FBUTt3REFDUlUsU0FDSXpCO3dEQUVKZ0IsVUFBVXBILGlHQUFDQTt3REFDWDhILGNBQVc7a0VBQWE7Ozs7Ozs7Ozs7OzhEQUtwQyw4REFBQ3BJLHlEQUFNQTtvREFDSG1JLFNBQVN4QjtvREFDVHJGLFVBQVUsQ0FBQytCO29EQUNYbkMsV0FBV1Qsa0RBQUVBLENBQ1QwQixZQUFZLEtBQUs7OERBRXBCRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVc3QztHQW43Qk10QjtLQUFBQTtBQXE3Qk4sK0RBQWVBLFVBQVVBLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvRGF0ZVJhbmdlLnRzeD85ZTY0Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xyXG5cclxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0J1xyXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXHJcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nXHJcbmltcG9ydCB7IENhbGVuZGFyIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhbGVuZGFyJ1xyXG5pbXBvcnQge1xyXG4gICAgUG9wb3ZlcixcclxuICAgIFBvcG92ZXJDb250ZW50LFxyXG4gICAgUG9wb3ZlclRyaWdnZXIsXHJcbn0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3BvcG92ZXInXHJcbmltcG9ydCB7IENhbGVuZGFySWNvbiwgWCwgQ2hlY2ssIHR5cGUgTHVjaWRlSWNvbiB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcclxuaW1wb3J0IHsgVGltZVBpY2tlciB9IGZyb20gJ0AvY29tcG9uZW50cy91aS90aW1lLXBpY2tlcidcclxuaW1wb3J0IHsgY24gfSBmcm9tICcuLi9hcHAvbGliL3V0aWxzJ1xyXG5pbXBvcnQgeyBmb3JtYXQsIGlzQWZ0ZXIsIGlzQmVmb3JlLCBzZXQgfSBmcm9tICdkYXRlLWZucydcclxuaW1wb3J0IHR5cGUgeyBEYXRlUmFuZ2UgfSBmcm9tICdyZWFjdC1kYXktcGlja2VyJ1xyXG5pbXBvcnQgeyBMYWJlbCwgTGFiZWxQb3NpdGlvbiB9IGZyb20gJy4vdWkvbGFiZWwnXHJcbmltcG9ydCB7IFNlcGFyYXRvciB9IGZyb20gJy4vdWkvc2VwYXJhdG9yJ1xyXG4vLyBTZWxlY3QgY29tcG9uZW50cyByZW1vdmVkIGluIGZhdm9yIG9mIFRpbWVQaWNrZXJcclxuXHJcbnR5cGUgRGF0ZVBpY2tlck1vZGUgPSAncmFuZ2UnIHwgJ3NpbmdsZSdcclxudHlwZSBUaW1lUGlja2VyTW9kZSA9ICdzaW5nbGUnIHwgJ3JhbmdlJ1xyXG50eXBlIERhdGVQaWNrZXJUeXBlID0gJ2RhdGUnIHwgJ2RhdGV0aW1lJ1xyXG5cclxuLy8gRGVmaW5lIGljb24gdHlwZSB0aGF0IGNhbiBiZSBlaXRoZXIgYSBSZWFjdCBub2RlIG9yIGEgTHVjaWRlIGljb24gY29tcG9uZW50XHJcbnR5cGUgSWNvblR5cGUgPSBSZWFjdC5SZWFjdE5vZGUgfCBMdWNpZGVJY29uXHJcblxyXG5pbnRlcmZhY2UgRGF0ZVZhbGlkYXRpb24ge1xyXG4gICAgbWluRGF0ZT86IERhdGVcclxuICAgIG1heERhdGU/OiBEYXRlXHJcbiAgICBkaXNhYmxlZERhdGVzPzogRGF0ZVtdXHJcbiAgICBkaXNhYmxlZERheXNPZldlZWs/OiBudW1iZXJbXSAvLyAwID0gU3VuZGF5LCA2ID0gU2F0dXJkYXlcclxufVxyXG5cclxuaW50ZXJmYWNlIERhdGVQaWNrZXJQcm9wc1xyXG4gICAgZXh0ZW5kcyBPbWl0PFxyXG4gICAgICAgIFJlYWN0LkJ1dHRvbkhUTUxBdHRyaWJ1dGVzPEhUTUxCdXR0b25FbGVtZW50PixcclxuICAgICAgICAndmFsdWUnIHwgJ29uQ2hhbmdlJyB8ICd0eXBlJ1xyXG4gICAgPiB7XHJcbiAgICBvbkNoYW5nZTogKHZhbHVlOiBhbnkpID0+IHZvaWRcclxuICAgIGNsYXNzTmFtZT86IHN0cmluZ1xyXG4gICAgcGxhY2Vob2xkZXI/OiBzdHJpbmdcclxuICAgIC8qKlxyXG4gICAgICogTW9kZSBvZiB0aGUgZGF0ZSBwaWNrZXIuXHJcbiAgICAgKiAncmFuZ2UnIGZvciBkYXRlIHJhbmdlIHNlbGVjdGlvbiAoZGVmYXVsdClcclxuICAgICAqICdzaW5nbGUnIGZvciBzaW5nbGUgZGF0ZSBzZWxlY3Rpb25cclxuICAgICAqL1xyXG4gICAgbW9kZT86IERhdGVQaWNrZXJNb2RlXHJcbiAgICAvKipcclxuICAgICAqIFR5cGUgb2YgdGhlIGRhdGUgcGlja2VyLlxyXG4gICAgICogJ2RhdGUnIGZvciBkYXRlIG9ubHkgc2VsZWN0aW9uIChkZWZhdWx0KVxyXG4gICAgICogJ2RhdGV0aW1lJyBmb3IgZGF0ZSBhbmQgdGltZSBzZWxlY3Rpb25cclxuICAgICAqL1xyXG4gICAgdHlwZT86IERhdGVQaWNrZXJUeXBlXHJcbiAgICBkaXNhYmxlZD86IGJvb2xlYW5cclxuICAgIHZhbHVlPzogRGF0ZSB8IERhdGVSYW5nZVxyXG4gICAgLyoqXHJcbiAgICAgKiBEYXRlIGZvcm1hdCBmb3IgZGlzcGxheVxyXG4gICAgICogQGRlZmF1bHQgJ0xMTCBkZCwgeSdcclxuICAgICAqL1xyXG4gICAgZGF0ZUZvcm1hdD86IHN0cmluZ1xyXG4gICAgLyoqIFZhbGlkYXRpb24gcnVsZXMgZm9yIGRhdGUgc2VsZWN0aW9uICovXHJcbiAgICB2YWxpZGF0aW9uPzogRGF0ZVZhbGlkYXRpb25cclxuICAgIC8qKiBOdW1iZXIgb2YgbW9udGhzIHRvIGRpc3BsYXkgaW4gdGhlIGNhbGVuZGFyICovXHJcbiAgICBudW1iZXJPZk1vbnRocz86IG51bWJlclxyXG4gICAgLyoqIFdoZXRoZXIgdG8gY2xvc2UgdGhlIHBvcG92ZXIgd2hlbiBhIGRhdGUgaXMgc2VsZWN0ZWQgKHNpbmdsZSBtb2RlIG9ubHkpICovXHJcbiAgICBjbG9zZU9uU2VsZWN0PzogYm9vbGVhblxyXG4gICAgLyoqIFdoZXRoZXIgdG8gc2hvdyB3ZWVrIG51bWJlcnMgKi9cclxuICAgIHNob3dXZWVrTnVtYmVycz86IGJvb2xlYW5cclxuICAgIC8qKiBXaGV0aGVyIHRvIGluY2x1ZGUgdGltZSBwaWNrZXIgKGRlcHJlY2F0ZWQsIHVzZSB0eXBlPSdkYXRldGltZScgaW5zdGVhZCkgKi9cclxuICAgIGluY2x1ZGVUaW1lPzogYm9vbGVhblxyXG4gICAgLyoqIE1vZGUgb2YgdGhlIHRpbWUgcGlja2VyOiAnc2luZ2xlJyBvciAncmFuZ2UnICovXHJcbiAgICB0aW1lTW9kZT86IFRpbWVQaWNrZXJNb2RlXHJcbiAgICAvKiogVGltZSBmb3JtYXQgZm9yIGRpc3BsYXkgKi9cclxuICAgIHRpbWVGb3JtYXQ/OiBzdHJpbmdcclxuICAgIC8qKiBJbnRlcnZhbCBiZXR3ZWVuIHRpbWUgb3B0aW9ucyBpbiBtaW51dGVzICovXHJcbiAgICB0aW1lSW50ZXJ2YWw/OiBudW1iZXJcclxuICAgIC8qKiBPcHRpb25hbCBsYWJlbCBmb3IgdGhlIGRhdGUgcGlja2VyICovXHJcbiAgICBsYWJlbD86IHN0cmluZ1xyXG4gICAgLyoqIFBvc2l0aW9uIG9mIHRoZSBsYWJlbCByZWxhdGl2ZSB0byB0aGUgZGF0ZSBwaWNrZXIgKi9cclxuICAgIGxhYmVsUG9zaXRpb24/OiBMYWJlbFBvc2l0aW9uXHJcbiAgICAvKiogV2hldGhlciB0byBzaG93IGEgY2xlYXIgYnV0dG9uIGluc2lkZSB0aGUgZGF0ZSBwaWNrZXIgKi9cclxuICAgIGNsZWFyYWJsZT86IGJvb2xlYW5cclxuICAgIC8qKlxyXG4gICAgICogQ3VzdG9tIGljb24gdG8gZGlzcGxheSBpbiB0aGUgZGF0ZSBwaWNrZXIgYnV0dG9uLlxyXG4gICAgICogQ2FuIGJlIGEgTHVjaWRlIGljb24gY29tcG9uZW50IChDYWxlbmRhckljb24pIG9yIGEgSlNYIGVsZW1lbnQgKDxDYWxlbmRhckljb24gLz4pXHJcbiAgICAgKi9cclxuICAgIGljb24/OiBJY29uVHlwZVxyXG4gICAgLyoqIFdoZXRoZXIgdG8gc2hvdyBhIGNvbmZpcm1hdGlvbiBidXR0b24gYWZ0ZXIgc2VsZWN0aW5nIGEgZGF0ZS90aW1lICovXHJcbiAgICBjb25maXJtU2VsZWN0aW9uPzogYm9vbGVhblxyXG4gICAgLyoqIFRleHQgdG8gZGlzcGxheSBvbiB0aGUgY29uZmlybWF0aW9uIGJ1dHRvbiAqL1xyXG4gICAgY29uZmlybUJ1dHRvblRleHQ/OiBzdHJpbmdcclxuICAgIC8qKiBXaGV0aGVyIHRvIHVzZSBhIG1vZGFsIGZvciB0aGUgZGF0ZSBwaWNrZXIgKi9cclxuICAgIG1vZGFsPzogYm9vbGVhblxyXG4gICAgd3JhcHBlckNsYXNzTmFtZT86IHN0cmluZ1xyXG59XHJcblxyXG5jb25zdCBEYXRlUGlja2VyID0gKHtcclxuICAgIG9uQ2hhbmdlLFxyXG4gICAgY2xhc3NOYW1lLFxyXG4gICAgcGxhY2Vob2xkZXIgPSAnU2VsZWN0IGRhdGUnLFxyXG4gICAgbW9kZSA9ICdyYW5nZScsXHJcbiAgICB0eXBlID0gJ2RhdGUnLFxyXG4gICAgZGlzYWJsZWQgPSBmYWxzZSxcclxuICAgIHZhbHVlLFxyXG4gICAgZGF0ZUZvcm1hdCA9ICdkZCBMTExMLCB5JyxcclxuICAgIHZhbGlkYXRpb24sXHJcbiAgICBudW1iZXJPZk1vbnRocyA9IG1vZGUgPT09ICdyYW5nZScgPyAyIDogMSxcclxuICAgIGNsb3NlT25TZWxlY3QgPSB0cnVlLFxyXG4gICAgc2hvd1dlZWtOdW1iZXJzID0gZmFsc2UsXHJcbiAgICBpbmNsdWRlVGltZSA9IGZhbHNlLCAvLyBkZXByZWNhdGVkLCB1c2UgdHlwZT0nZGF0ZXRpbWUnIGluc3RlYWRcclxuICAgIHRpbWVNb2RlID0gJ3NpbmdsZScsXHJcbiAgICB0aW1lRm9ybWF0ID0gJ0hIOm1tJyxcclxuICAgIHRpbWVJbnRlcnZhbCA9IDMwLFxyXG4gICAgbGFiZWwsXHJcbiAgICBsYWJlbFBvc2l0aW9uID0gJ3RvcCcsXHJcbiAgICBjbGVhcmFibGUgPSBmYWxzZSxcclxuICAgIGljb24sXHJcbiAgICBjb25maXJtU2VsZWN0aW9uID0gdHJ1ZSxcclxuICAgIGNvbmZpcm1CdXR0b25UZXh0ID0gJ0NvbmZpcm0nLFxyXG4gICAgbW9kYWwgPSBmYWxzZSxcclxuICAgIHdyYXBwZXJDbGFzc05hbWUgPSAnJyxcclxuICAgIC4uLmJ1dHRvblByb3BzXHJcbn06IERhdGVQaWNrZXJQcm9wcykgPT4ge1xyXG4gICAgY29uc3QgW2RhdGVWYWx1ZSwgc2V0RGF0ZVZhbHVlXSA9IHVzZVN0YXRlPERhdGVSYW5nZSB8IERhdGUgfCB1bmRlZmluZWQ+KFxyXG4gICAgICAgIHZhbHVlIHx8XHJcbiAgICAgICAgICAgIChtb2RlID09PSAncmFuZ2UnID8geyBmcm9tOiB1bmRlZmluZWQsIHRvOiB1bmRlZmluZWQgfSA6IHVuZGVmaW5lZCksXHJcbiAgICApXHJcblxyXG4gICAgLy8gV2UnbGwgdXNlIGJ1dHRvblByb3BzIGRpcmVjdGx5IGJ1dCBlbnN1cmUgd2UgZG9uJ3QgcGFzcyBvdXIgY3VzdG9tIHR5cGUgcHJvcCB0byB0aGUgQnV0dG9uIGNvbXBvbmVudFxyXG4gICAgY29uc3QgW29wZW4sIHNldE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpXHJcbiAgICBjb25zdCBbdGltZSwgc2V0VGltZV0gPSB1c2VTdGF0ZTx7IGhvdXI6IG51bWJlcjsgbWludXRlOiBudW1iZXIgfSB8IG51bGw+KFxyXG4gICAgICAgIG51bGwsXHJcbiAgICApXHJcbiAgICBjb25zdCBbdG9UaW1lLCBzZXRUb1RpbWVdID0gdXNlU3RhdGU8e1xyXG4gICAgICAgIGhvdXI6IG51bWJlclxyXG4gICAgICAgIG1pbnV0ZTogbnVtYmVyXHJcbiAgICB9IHwgbnVsbD4obnVsbClcclxuXHJcbiAgICAvLyBTdGF0ZSB0byB0cmFjayBwZW5kaW5nIHNlbGVjdGlvbiB3aGVuIGNvbmZpcm1hdGlvbiBidXR0b24gaXMgZW5hYmxlZFxyXG4gICAgY29uc3QgW3BlbmRpbmdTZWxlY3Rpb24sIHNldFBlbmRpbmdTZWxlY3Rpb25dID0gdXNlU3RhdGU8XHJcbiAgICAgICAgRGF0ZVJhbmdlIHwgRGF0ZSB8IHVuZGVmaW5lZFxyXG4gICAgPih2YWx1ZSB8fCB1bmRlZmluZWQpXHJcblxyXG4gICAgLy8gU2V0IHBsYWNlaG9sZGVyIGJhc2VkIG9uIG1vZGVcclxuICAgIGNvbnN0IGFjdHVhbFBsYWNlaG9sZGVyID1cclxuICAgICAgICBtb2RlID09PSAncmFuZ2UnID8gJ1NlbGVjdCBkYXRlIHJhbmdlJyA6ICdTZWxlY3QgZGF0ZSdcclxuXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIC8vIERlZXAgZXF1YWxpdHkgY2hlY2sgZm9yIGRhdGVzIHRvIHByZXZlbnQgdW5uZWNlc3NhcnkgdXBkYXRlc1xyXG4gICAgICAgIGNvbnN0IGlzRXF1YWwgPSAoYTogYW55LCBiOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgaWYgKGEgPT09IGIpIHJldHVybiB0cnVlXHJcblxyXG4gICAgICAgICAgICAvLyBIYW5kbGUgRGF0ZSBvYmplY3RzXHJcbiAgICAgICAgICAgIGlmIChhIGluc3RhbmNlb2YgRGF0ZSAmJiBiIGluc3RhbmNlb2YgRGF0ZSkge1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIGEuZ2V0VGltZSgpID09PSBiLmdldFRpbWUoKVxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAvLyBIYW5kbGUgRGF0ZVJhbmdlIG9iamVjdHNcclxuICAgICAgICAgICAgaWYgKGEgJiYgYiAmJiB0eXBlb2YgYSA9PT0gJ29iamVjdCcgJiYgdHlwZW9mIGIgPT09ICdvYmplY3QnKSB7XHJcbiAgICAgICAgICAgICAgICBpZiAoJ2Zyb20nIGluIGEgJiYgJ3RvJyBpbiBhICYmICdmcm9tJyBpbiBiICYmICd0bycgaW4gYikge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGZyb21FcXVhbCA9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGEuZnJvbSAmJiBiLmZyb21cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gYS5mcm9tIGluc3RhbmNlb2YgRGF0ZSAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBiLmZyb20gaW5zdGFuY2VvZiBEYXRlICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGEuZnJvbS5nZXRUaW1lKCkgPT09IGIuZnJvbS5nZXRUaW1lKClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogYS5mcm9tID09PSBiLmZyb21cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdG9FcXVhbCA9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGEudG8gJiYgYi50b1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBhLnRvIGluc3RhbmNlb2YgRGF0ZSAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBiLnRvIGluc3RhbmNlb2YgRGF0ZSAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhLnRvLmdldFRpbWUoKSA9PT0gYi50by5nZXRUaW1lKClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogYS50byA9PT0gYi50b1xyXG5cclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gZnJvbUVxdWFsICYmIHRvRXF1YWxcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgcmV0dXJuIGZhbHNlXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBPbmx5IHVwZGF0ZSBpZiB0aGUgdmFsdWUgaGFzIGFjdHVhbGx5IGNoYW5nZWRcclxuICAgICAgICBpZiAoIWlzRXF1YWwodmFsdWUsIGRhdGVWYWx1ZSkpIHtcclxuICAgICAgICAgICAgaWYgKHZhbHVlKSB7XHJcbiAgICAgICAgICAgICAgICBzZXREYXRlVmFsdWUodmFsdWUpXHJcbiAgICAgICAgICAgICAgICAvLyBBbHNvIHVwZGF0ZSBwZW5kaW5nU2VsZWN0aW9uIHdpdGggdGhlIHZhbHVlXHJcbiAgICAgICAgICAgICAgICBzZXRQZW5kaW5nU2VsZWN0aW9uKHZhbHVlKVxyXG5cclxuICAgICAgICAgICAgICAgIGlmICh2YWx1ZSBpbnN0YW5jZW9mIERhdGUpIHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBob3VycyA9IHZhbHVlLmdldEhvdXJzKClcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBtaW51dGVzID0gdmFsdWUuZ2V0TWludXRlcygpXHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKGhvdXJzIHx8IG1pbnV0ZXMpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldFRpbWUoeyBob3VyOiBob3VycywgbWludXRlOiBtaW51dGVzIH0pXHJcbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKCdmcm9tJyBpbiB2YWx1ZSAmJiB2YWx1ZS5mcm9tIGluc3RhbmNlb2YgRGF0ZSkge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHsgZnJvbSwgdG8gfSA9IHZhbHVlXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZnJvbUhvdXJzID0gZnJvbS5nZXRIb3VycygpXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZnJvbU1pbnV0ZXMgPSBmcm9tLmdldE1pbnV0ZXMoKVxyXG4gICAgICAgICAgICAgICAgICAgIGlmIChmcm9tSG91cnMgfHwgZnJvbU1pbnV0ZXMpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldFRpbWUoeyBob3VyOiBmcm9tSG91cnMsIG1pbnV0ZTogZnJvbU1pbnV0ZXMgfSlcclxuICAgICAgICAgICAgICAgICAgICBpZiAodG8gaW5zdGFuY2VvZiBEYXRlKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHRvSG91cnMgPSB0by5nZXRIb3VycygpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHRvTWludXRlcyA9IHRvLmdldE1pbnV0ZXMoKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAodG9Ib3VycyB8fCB0b01pbnV0ZXMpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRUb1RpbWUoeyBob3VyOiB0b0hvdXJzLCBtaW51dGU6IHRvTWludXRlcyB9KVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIC8vIFdoZW4gdmFsdWUgaXMgbnVsbC91bmRlZmluZWQsIHJlc2V0IHRvIGluaXRpYWwgc3RhdGUgYnV0IG1haW50YWluIGZvcm1hdFxyXG4gICAgICAgICAgICAgICAgc2V0RGF0ZVZhbHVlKFxyXG4gICAgICAgICAgICAgICAgICAgIG1vZGUgPT09ICdyYW5nZSdcclxuICAgICAgICAgICAgICAgICAgICAgICAgPyB7IGZyb206IHVuZGVmaW5lZCwgdG86IHVuZGVmaW5lZCB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogdW5kZWZpbmVkLFxyXG4gICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgLy8gQWxzbyByZXNldCBwZW5kaW5nU2VsZWN0aW9uXHJcbiAgICAgICAgICAgICAgICBzZXRQZW5kaW5nU2VsZWN0aW9uKHVuZGVmaW5lZClcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH0sIFt2YWx1ZSwgbW9kZV0pXHJcblxyXG4gICAgY29uc3QgdmFsaWRhdGVEYXRlID0gKGRhdGU6IERhdGUpOiBib29sZWFuID0+IHtcclxuICAgICAgICBpZiAoIXZhbGlkYXRpb24pIHJldHVybiB0cnVlXHJcbiAgICAgICAgY29uc3QgeyBtaW5EYXRlLCBtYXhEYXRlLCBkaXNhYmxlZERhdGVzLCBkaXNhYmxlZERheXNPZldlZWsgfSA9XHJcbiAgICAgICAgICAgIHZhbGlkYXRpb25cclxuICAgICAgICBpZiAobWluRGF0ZSAmJiBpc0JlZm9yZShkYXRlLCBtaW5EYXRlKSkgcmV0dXJuIGZhbHNlXHJcbiAgICAgICAgaWYgKG1heERhdGUgJiYgaXNBZnRlcihkYXRlLCBtYXhEYXRlKSkgcmV0dXJuIGZhbHNlXHJcbiAgICAgICAgaWYgKFxyXG4gICAgICAgICAgICBkaXNhYmxlZERhdGVzPy5zb21lKFxyXG4gICAgICAgICAgICAgICAgKGRpc2FibGVkRGF0ZSkgPT5cclxuICAgICAgICAgICAgICAgICAgICBmb3JtYXQoZGlzYWJsZWREYXRlLCAneXl5eS1NTS1kZCcpID09PVxyXG4gICAgICAgICAgICAgICAgICAgIGZvcm1hdChkYXRlLCAneXl5eS1NTS1kZCcpLFxyXG4gICAgICAgICAgICApXHJcbiAgICAgICAgKVxyXG4gICAgICAgICAgICByZXR1cm4gZmFsc2VcclxuICAgICAgICBpZiAoZGlzYWJsZWREYXlzT2ZXZWVrPy5pbmNsdWRlcyhkYXRlLmdldERheSgpKSkgcmV0dXJuIGZhbHNlXHJcbiAgICAgICAgcmV0dXJuIHRydWVcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBhcHBseVRpbWUgPSAoXHJcbiAgICAgICAgZGF0ZTogRGF0ZSB8IHVuZGVmaW5lZCxcclxuICAgICAgICB0OiB7IGhvdXI6IG51bWJlcjsgbWludXRlOiBudW1iZXIgfSB8IG51bGwsXHJcbiAgICApID0+XHJcbiAgICAgICAgZGF0ZSAmJiB0XHJcbiAgICAgICAgICAgID8gc2V0KGRhdGUsIHtcclxuICAgICAgICAgICAgICAgICAgaG91cnM6IHQuaG91cixcclxuICAgICAgICAgICAgICAgICAgbWludXRlczogdC5taW51dGUsXHJcbiAgICAgICAgICAgICAgICAgIHNlY29uZHM6IDAsXHJcbiAgICAgICAgICAgICAgICAgIG1pbGxpc2Vjb25kczogMCxcclxuICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICA6IGRhdGVcclxuXHJcbiAgICBjb25zdCBoYW5kbGVWYWx1ZUNoYW5nZSA9IChuZXdWYWx1ZTogRGF0ZVJhbmdlIHwgRGF0ZSB8IHVuZGVmaW5lZCkgPT4ge1xyXG4gICAgICAgIGlmICghbmV3VmFsdWUpIHtcclxuICAgICAgICAgICAgLy8gV2hlbiBhIGRhdGUgaXMgdW5zZWxlY3RlZCwgbWFpbnRhaW4gdGhlIGZvcm1hdCBjb25zaXN0ZW5jeVxyXG4gICAgICAgICAgICBzZXREYXRlVmFsdWUoXHJcbiAgICAgICAgICAgICAgICBtb2RlID09PSAncmFuZ2UnXHJcbiAgICAgICAgICAgICAgICAgICAgPyB7IGZyb206IHVuZGVmaW5lZCwgdG86IHVuZGVmaW5lZCB9XHJcbiAgICAgICAgICAgICAgICAgICAgOiB1bmRlZmluZWQsXHJcbiAgICAgICAgICAgIClcclxuICAgICAgICAgICAgc2V0UGVuZGluZ1NlbGVjdGlvbih1bmRlZmluZWQpXHJcbiAgICAgICAgICAgIG9uQ2hhbmdlKG51bGwpXHJcbiAgICAgICAgICAgIHJldHVyblxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgaWYgKG1vZGUgPT09ICdyYW5nZScpIHtcclxuICAgICAgICAgICAgY29uc3QgeyBmcm9tLCB0byB9ID0gbmV3VmFsdWUgYXMgRGF0ZVJhbmdlXHJcbiAgICAgICAgICAgIGlmIChmcm9tICYmICF2YWxpZGF0ZURhdGUoZnJvbSkpIHJldHVyblxyXG4gICAgICAgICAgICBpZiAodG8gJiYgIXZhbGlkYXRlRGF0ZSh0bykpIHJldHVyblxyXG5cclxuICAgICAgICAgICAgLy8gSWYgY29uZmlybWF0aW9uIGlzIHJlcXVpcmVkLCBzdG9yZSB0aGUgc2VsZWN0aW9uIGluIHBlbmRpbmcgc3RhdGVcclxuICAgICAgICAgICAgaWYgKGNvbmZpcm1TZWxlY3Rpb24pIHtcclxuICAgICAgICAgICAgICAgIHNldFBlbmRpbmdTZWxlY3Rpb24oeyBmcm9tLCB0byB9KVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgc2V0RGF0ZVZhbHVlKHsgZnJvbSwgdG8gfSlcclxuXHJcbiAgICAgICAgICAgICAgICAvLyBJZiB0aW1lIGlzIG5vdCBzZXQsIGluaXRpYWxpemUgaXQgd2l0aCBjdXJyZW50IHRpbWVcclxuICAgICAgICAgICAgICAgIGxldCBjdXJyZW50VGltZSA9IHRpbWVcclxuICAgICAgICAgICAgICAgIGlmICghY3VycmVudFRpbWUgJiYgc2hvdWxkSW5jbHVkZVRpbWUpIHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpXHJcbiAgICAgICAgICAgICAgICAgICAgY3VycmVudFRpbWUgPSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGhvdXI6IG5vdy5nZXRIb3VycygpLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBtaW51dGU6IG5vdy5nZXRNaW51dGVzKCksXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIHNldFRpbWUoY3VycmVudFRpbWUpXHJcbiAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgbGV0IGN1cnJlbnRUb1RpbWUgPSB0b1RpbWVcclxuICAgICAgICAgICAgICAgIGlmICghY3VycmVudFRvVGltZSAmJiBzaG91bGRJbmNsdWRlVGltZSAmJiB0bykge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKClcclxuICAgICAgICAgICAgICAgICAgICBjdXJyZW50VG9UaW1lID0ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBob3VyOiBub3cuZ2V0SG91cnMoKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgbWludXRlOiBub3cuZ2V0TWludXRlcygpLFxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICBzZXRUb1RpbWUoY3VycmVudFRvVGltZSlcclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZSh7XHJcbiAgICAgICAgICAgICAgICAgICAgc3RhcnREYXRlOiBhcHBseVRpbWUoZnJvbSwgY3VycmVudFRpbWUpLFxyXG4gICAgICAgICAgICAgICAgICAgIGVuZERhdGU6IGFwcGx5VGltZSh0bywgY3VycmVudFRvVGltZSksXHJcbiAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgY29uc3Qgc2luZ2xlRGF0ZSA9IG5ld1ZhbHVlIGFzIERhdGVcclxuICAgICAgICAgICAgaWYgKCF2YWxpZGF0ZURhdGUoc2luZ2xlRGF0ZSkpIHJldHVyblxyXG5cclxuICAgICAgICAgICAgLy8gSWYgY29uZmlybWF0aW9uIGlzIHJlcXVpcmVkLCBzdG9yZSB0aGUgc2VsZWN0aW9uIGluIHBlbmRpbmcgc3RhdGVcclxuICAgICAgICAgICAgaWYgKGNvbmZpcm1TZWxlY3Rpb24pIHtcclxuICAgICAgICAgICAgICAgIHNldFBlbmRpbmdTZWxlY3Rpb24oc2luZ2xlRGF0ZSlcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIHNldERhdGVWYWx1ZShzaW5nbGVEYXRlKVxyXG5cclxuICAgICAgICAgICAgICAgIC8vIElmIHRpbWUgaXMgbm90IHNldCBhbmQgd2Ugc2hvdWxkIGluY2x1ZGUgdGltZSwgcHJlc2VydmUgZXhpc3RpbmcgdGltZSBvciBpbml0aWFsaXplXHJcbiAgICAgICAgICAgICAgICBsZXQgY3VycmVudFRpbWUgPSB0aW1lXHJcblxyXG4gICAgICAgICAgICAgICAgLy8gSWYgd2UgaGF2ZSBhbiBleGlzdGluZyBkYXRlVmFsdWUgd2l0aCB0aW1lLCBwcmVzZXJ2ZSB0aGF0IHRpbWVcclxuICAgICAgICAgICAgICAgIGlmIChcclxuICAgICAgICAgICAgICAgICAgICAhY3VycmVudFRpbWUgJiZcclxuICAgICAgICAgICAgICAgICAgICBzaG91bGRJbmNsdWRlVGltZSAmJlxyXG4gICAgICAgICAgICAgICAgICAgIGRhdGVWYWx1ZSBpbnN0YW5jZW9mIERhdGVcclxuICAgICAgICAgICAgICAgICkge1xyXG4gICAgICAgICAgICAgICAgICAgIGN1cnJlbnRUaW1lID0ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBob3VyOiBkYXRlVmFsdWUuZ2V0SG91cnMoKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgbWludXRlOiBkYXRlVmFsdWUuZ2V0TWludXRlcygpLFxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICBzZXRUaW1lKGN1cnJlbnRUaW1lKVxyXG4gICAgICAgICAgICAgICAgfSBlbHNlIGlmICghY3VycmVudFRpbWUgJiYgc2hvdWxkSW5jbHVkZVRpbWUpIHtcclxuICAgICAgICAgICAgICAgICAgICAvLyBPbmx5IHVzZSBjdXJyZW50IHRpbWUgaWYgbm8gZXhpc3RpbmcgdGltZSBpcyBhdmFpbGFibGVcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpXHJcbiAgICAgICAgICAgICAgICAgICAgY3VycmVudFRpbWUgPSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGhvdXI6IG5vdy5nZXRIb3VycygpLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBtaW51dGU6IG5vdy5nZXRNaW51dGVzKCksXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIHNldFRpbWUoY3VycmVudFRpbWUpXHJcbiAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgLy8gQWx3YXlzIGFwcGx5IHRpbWUgaWYgc2hvdWxkSW5jbHVkZVRpbWUgaXMgdHJ1ZVxyXG4gICAgICAgICAgICAgICAgY29uc3QgcmVzdWx0ID0gc2hvdWxkSW5jbHVkZVRpbWVcclxuICAgICAgICAgICAgICAgICAgICA/IGFwcGx5VGltZShzaW5nbGVEYXRlLCBjdXJyZW50VGltZSlcclxuICAgICAgICAgICAgICAgICAgICA6IHNpbmdsZURhdGVcclxuXHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZShyZXN1bHQpXHJcblxyXG4gICAgICAgICAgICAgICAgaWYgKGNsb3NlT25TZWxlY3QgJiYgIXNob3VsZEluY2x1ZGVUaW1lKSBzZXRPcGVuKGZhbHNlKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGhhbmRsZVRpbWVDaGFuZ2UgPSAoZGF0ZTogRGF0ZSwgaXNUb1RpbWUgPSBmYWxzZSkgPT4ge1xyXG4gICAgICAgIGlmICghZGF0ZSkgcmV0dXJuXHJcblxyXG4gICAgICAgIGNvbnN0IG5ld1RpbWUgPSB7IGhvdXI6IGRhdGUuZ2V0SG91cnMoKSwgbWludXRlOiBkYXRlLmdldE1pbnV0ZXMoKSB9XHJcblxyXG4gICAgICAgIC8vIENoZWNrIGlmIHRoZSB0aW1lIGhhcyBhY3R1YWxseSBjaGFuZ2VkIGJlZm9yZSB1cGRhdGluZyBzdGF0ZVxyXG4gICAgICAgIGlmIChpc1RvVGltZSkge1xyXG4gICAgICAgICAgICBjb25zdCBjdXJyZW50VG9UaW1lID0gdG9UaW1lXHJcblxyXG4gICAgICAgICAgICBpZiAoXHJcbiAgICAgICAgICAgICAgICAhY3VycmVudFRvVGltZSB8fFxyXG4gICAgICAgICAgICAgICAgY3VycmVudFRvVGltZS5ob3VyICE9PSBuZXdUaW1lLmhvdXIgfHxcclxuICAgICAgICAgICAgICAgIGN1cnJlbnRUb1RpbWUubWludXRlICE9PSBuZXdUaW1lLm1pbnV0ZVxyXG4gICAgICAgICAgICApIHtcclxuICAgICAgICAgICAgICAgIHNldFRvVGltZShuZXdUaW1lKVxyXG5cclxuICAgICAgICAgICAgICAgIC8vIFVwZGF0ZSBwZW5kaW5nU2VsZWN0aW9uIHdpdGggdGhlIG5ldyB0aW1lXHJcbiAgICAgICAgICAgICAgICBpZiAoY29uZmlybVNlbGVjdGlvbiAmJiAocGVuZGluZ1NlbGVjdGlvbiBhcyBEYXRlUmFuZ2UpPy50bykge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld0VuZERhdGUgPSBzZXQoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIChwZW5kaW5nU2VsZWN0aW9uIGFzIERhdGVSYW5nZSkudG8hLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBob3VyczogbmV3VGltZS5ob3VyLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbWludXRlczogbmV3VGltZS5taW51dGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWNvbmRzOiAwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbWlsbGlzZWNvbmRzOiAwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgIClcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0UGVuZGluZ1NlbGVjdGlvbih7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLihwZW5kaW5nU2VsZWN0aW9uIGFzIERhdGVSYW5nZSksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRvOiBuZXdFbmREYXRlLFxyXG4gICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgLy8gSWYgY29uZmlybWF0aW9uIGlzIG5vdCByZXF1aXJlZCwgY2FsbCBvbkNoYW5nZSBkaXJlY3RseVxyXG4gICAgICAgICAgICAgICAgaWYgKCFjb25maXJtU2VsZWN0aW9uKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKChkYXRlVmFsdWUgYXMgRGF0ZVJhbmdlKT8udG8pIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgbmV3RW5kRGF0ZSA9IHNldCgoZGF0ZVZhbHVlIGFzIERhdGVSYW5nZSkudG8hLCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBob3VyczogbmV3VGltZS5ob3VyLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbWludXRlczogbmV3VGltZS5taW51dGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWNvbmRzOiAwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbWlsbGlzZWNvbmRzOiAwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9KVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2Uoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhcnREYXRlOiAoZGF0ZVZhbHVlIGFzIERhdGVSYW5nZSkuZnJvbSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVuZERhdGU6IG5ld0VuZERhdGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgY29uc3QgY3VycmVudFRpbWUgPSB0aW1lXHJcblxyXG4gICAgICAgICAgICBpZiAoXHJcbiAgICAgICAgICAgICAgICAhY3VycmVudFRpbWUgfHxcclxuICAgICAgICAgICAgICAgIGN1cnJlbnRUaW1lLmhvdXIgIT09IG5ld1RpbWUuaG91ciB8fFxyXG4gICAgICAgICAgICAgICAgY3VycmVudFRpbWUubWludXRlICE9PSBuZXdUaW1lLm1pbnV0ZVxyXG4gICAgICAgICAgICApIHtcclxuICAgICAgICAgICAgICAgIHNldFRpbWUobmV3VGltZSlcclxuXHJcbiAgICAgICAgICAgICAgICAvLyBVcGRhdGUgcGVuZGluZ1NlbGVjdGlvbiB3aXRoIHRoZSBuZXcgdGltZVxyXG4gICAgICAgICAgICAgICAgaWYgKGNvbmZpcm1TZWxlY3Rpb24pIHtcclxuICAgICAgICAgICAgICAgICAgICBpZiAoKHBlbmRpbmdTZWxlY3Rpb24gYXMgRGF0ZVJhbmdlKT8uZnJvbSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBuZXdTdGFydERhdGUgPSBzZXQoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAocGVuZGluZ1NlbGVjdGlvbiBhcyBEYXRlUmFuZ2UpLmZyb20hLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhvdXJzOiBuZXdUaW1lLmhvdXIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWludXRlczogbmV3VGltZS5taW51dGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2Vjb25kczogMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtaWxsaXNlY29uZHM6IDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICApXHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRQZW5kaW5nU2VsZWN0aW9uKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLihwZW5kaW5nU2VsZWN0aW9uIGFzIERhdGVSYW5nZSksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmcm9tOiBuZXdTdGFydERhdGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmIChwZW5kaW5nU2VsZWN0aW9uIGluc3RhbmNlb2YgRGF0ZSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBuZXdEYXRlID0gc2V0KHBlbmRpbmdTZWxlY3Rpb24gYXMgRGF0ZSwge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaG91cnM6IG5ld1RpbWUuaG91cixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pbnV0ZXM6IG5ld1RpbWUubWludXRlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2Vjb25kczogMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pbGxpc2Vjb25kczogMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSlcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldFBlbmRpbmdTZWxlY3Rpb24obmV3RGF0ZSlcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgLy8gSWYgY29uZmlybWF0aW9uIGlzIG5vdCByZXF1aXJlZCwgY2FsbCBvbkNoYW5nZSBkaXJlY3RseVxyXG4gICAgICAgICAgICAgICAgaWYgKCFjb25maXJtU2VsZWN0aW9uKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKChkYXRlVmFsdWUgYXMgRGF0ZVJhbmdlKT8uZnJvbSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBuZXdTdGFydERhdGUgPSBzZXQoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAoZGF0ZVZhbHVlIGFzIERhdGVSYW5nZSkuZnJvbSEsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaG91cnM6IG5ld1RpbWUuaG91cixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtaW51dGVzOiBuZXdUaW1lLm1pbnV0ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWNvbmRzOiAwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pbGxpc2Vjb25kczogMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIClcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0YXJ0RGF0ZTogbmV3U3RhcnREYXRlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZW5kRGF0ZTogKGRhdGVWYWx1ZSBhcyBEYXRlUmFuZ2UpLnRvLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoZGF0ZVZhbHVlIGluc3RhbmNlb2YgRGF0ZSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBuZXdEYXRlID0gc2V0KGRhdGVWYWx1ZSwge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaG91cnM6IG5ld1RpbWUuaG91cixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pbnV0ZXM6IG5ld1RpbWUubWludXRlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2Vjb25kczogMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pbGxpc2Vjb25kczogMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSlcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlKG5ld0RhdGUpXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGhhbmRsZUNsZWFyID0gKGU6IFJlYWN0Lk1vdXNlRXZlbnQ8SFRNTEJ1dHRvbkVsZW1lbnQ+KSA9PiB7XHJcbiAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKSAvLyBQcmV2ZW50IHRyaWdnZXJpbmcgdGhlIHBvcG92ZXJcclxuICAgICAgICBzZXREYXRlVmFsdWUoXHJcbiAgICAgICAgICAgIG1vZGUgPT09ICdyYW5nZScgPyB7IGZyb206IHVuZGVmaW5lZCwgdG86IHVuZGVmaW5lZCB9IDogdW5kZWZpbmVkLFxyXG4gICAgICAgIClcclxuICAgICAgICBzZXRQZW5kaW5nU2VsZWN0aW9uKHVuZGVmaW5lZClcclxuICAgICAgICBzZXRUaW1lKG51bGwpXHJcbiAgICAgICAgc2V0VG9UaW1lKG51bGwpXHJcbiAgICAgICAgb25DaGFuZ2UobnVsbClcclxuICAgIH1cclxuXHJcbiAgICAvLyBGdW5jdGlvbiB0byBoYW5kbGUgY2xlYXIgYnV0dG9uIGNsaWNrIGluc2lkZSB0aGUgcG9wb3ZlclxyXG4gICAgY29uc3QgaGFuZGxlQ2xlYXJJblBvcG92ZXIgPSAoKSA9PiB7XHJcbiAgICAgICAgc2V0RGF0ZVZhbHVlKFxyXG4gICAgICAgICAgICBtb2RlID09PSAncmFuZ2UnID8geyBmcm9tOiB1bmRlZmluZWQsIHRvOiB1bmRlZmluZWQgfSA6IHVuZGVmaW5lZCxcclxuICAgICAgICApXHJcbiAgICAgICAgc2V0UGVuZGluZ1NlbGVjdGlvbih1bmRlZmluZWQpXHJcbiAgICAgICAgc2V0VGltZShudWxsKVxyXG4gICAgICAgIHNldFRvVGltZShudWxsKVxyXG4gICAgICAgIG9uQ2hhbmdlKG51bGwpXHJcbiAgICAgICAgLy8gQ2xvc2UgdGhlIHBvcG92ZXIgYWZ0ZXIgY2xlYXJpbmdcclxuICAgICAgICBzZXRPcGVuKGZhbHNlKVxyXG4gICAgfVxyXG5cclxuICAgIC8vIEZ1bmN0aW9uIHRvIGhhbmRsZSBjb25maXJtYXRpb24gYnV0dG9uIGNsaWNrXHJcbiAgICBjb25zdCBoYW5kbGVDb25maXJtID0gKCkgPT4ge1xyXG4gICAgICAgIGlmICghcGVuZGluZ1NlbGVjdGlvbikgcmV0dXJuXHJcblxyXG4gICAgICAgIGlmIChtb2RlID09PSAncmFuZ2UnKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHsgZnJvbSwgdG8gfSA9IHBlbmRpbmdTZWxlY3Rpb24gYXMgRGF0ZVJhbmdlXHJcbiAgICAgICAgICAgIHNldERhdGVWYWx1ZSh7IGZyb20sIHRvIH0pXHJcblxyXG4gICAgICAgICAgICAvLyBBcHBseSB0aW1lIGlmIG5lZWRlZFxyXG4gICAgICAgICAgICBsZXQgY3VycmVudFRpbWUgPSB0aW1lXHJcbiAgICAgICAgICAgIGlmICghY3VycmVudFRpbWUgJiYgc2hvdWxkSW5jbHVkZVRpbWUgJiYgZnJvbSkge1xyXG4gICAgICAgICAgICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKVxyXG4gICAgICAgICAgICAgICAgY3VycmVudFRpbWUgPSB7IGhvdXI6IG5vdy5nZXRIb3VycygpLCBtaW51dGU6IG5vdy5nZXRNaW51dGVzKCkgfVxyXG4gICAgICAgICAgICAgICAgc2V0VGltZShjdXJyZW50VGltZSlcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgbGV0IGN1cnJlbnRUb1RpbWUgPSB0b1RpbWVcclxuICAgICAgICAgICAgaWYgKCFjdXJyZW50VG9UaW1lICYmIHNob3VsZEluY2x1ZGVUaW1lICYmIHRvKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpXHJcbiAgICAgICAgICAgICAgICBjdXJyZW50VG9UaW1lID0ge1xyXG4gICAgICAgICAgICAgICAgICAgIGhvdXI6IG5vdy5nZXRIb3VycygpLFxyXG4gICAgICAgICAgICAgICAgICAgIG1pbnV0ZTogbm93LmdldE1pbnV0ZXMoKSxcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIHNldFRvVGltZShjdXJyZW50VG9UaW1lKVxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICBvbkNoYW5nZSh7XHJcbiAgICAgICAgICAgICAgICBzdGFydERhdGU6IGFwcGx5VGltZShmcm9tLCBjdXJyZW50VGltZSksXHJcbiAgICAgICAgICAgICAgICBlbmREYXRlOiBhcHBseVRpbWUodG8sIGN1cnJlbnRUb1RpbWUpLFxyXG4gICAgICAgICAgICB9KVxyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHNpbmdsZURhdGUgPSBwZW5kaW5nU2VsZWN0aW9uIGFzIERhdGVcclxuICAgICAgICAgICAgc2V0RGF0ZVZhbHVlKHNpbmdsZURhdGUpXHJcblxyXG4gICAgICAgICAgICAvLyBBcHBseSB0aW1lIGlmIG5lZWRlZFxyXG4gICAgICAgICAgICBsZXQgY3VycmVudFRpbWUgPSB0aW1lXHJcblxyXG4gICAgICAgICAgICAvLyBJZiB3ZSBoYXZlIGFuIGV4aXN0aW5nIGRhdGVWYWx1ZSB3aXRoIHRpbWUsIHByZXNlcnZlIHRoYXQgdGltZVxyXG4gICAgICAgICAgICBpZiAoXHJcbiAgICAgICAgICAgICAgICAhY3VycmVudFRpbWUgJiZcclxuICAgICAgICAgICAgICAgIHNob3VsZEluY2x1ZGVUaW1lICYmXHJcbiAgICAgICAgICAgICAgICBkYXRlVmFsdWUgaW5zdGFuY2VvZiBEYXRlXHJcbiAgICAgICAgICAgICkge1xyXG4gICAgICAgICAgICAgICAgY3VycmVudFRpbWUgPSB7XHJcbiAgICAgICAgICAgICAgICAgICAgaG91cjogZGF0ZVZhbHVlLmdldEhvdXJzKCksXHJcbiAgICAgICAgICAgICAgICAgICAgbWludXRlOiBkYXRlVmFsdWUuZ2V0TWludXRlcygpLFxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgc2V0VGltZShjdXJyZW50VGltZSlcclxuICAgICAgICAgICAgfSBlbHNlIGlmICghY3VycmVudFRpbWUgJiYgc2hvdWxkSW5jbHVkZVRpbWUpIHtcclxuICAgICAgICAgICAgICAgIC8vIE9ubHkgdXNlIGN1cnJlbnQgdGltZSBpZiBubyBleGlzdGluZyB0aW1lIGlzIGF2YWlsYWJsZVxyXG4gICAgICAgICAgICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKVxyXG4gICAgICAgICAgICAgICAgY3VycmVudFRpbWUgPSB7IGhvdXI6IG5vdy5nZXRIb3VycygpLCBtaW51dGU6IG5vdy5nZXRNaW51dGVzKCkgfVxyXG4gICAgICAgICAgICAgICAgc2V0VGltZShjdXJyZW50VGltZSlcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgY29uc3QgcmVzdWx0ID0gc2hvdWxkSW5jbHVkZVRpbWVcclxuICAgICAgICAgICAgICAgID8gYXBwbHlUaW1lKHNpbmdsZURhdGUsIGN1cnJlbnRUaW1lKVxyXG4gICAgICAgICAgICAgICAgOiBzaW5nbGVEYXRlXHJcblxyXG4gICAgICAgICAgICBvbkNoYW5nZShyZXN1bHQpXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBDbG9zZSB0aGUgcG9wb3ZlciBhZnRlciBjb25maXJtYXRpb25cclxuICAgICAgICBzZXRPcGVuKGZhbHNlKVxyXG4gICAgICAgIC8vIFJlc2V0IHBlbmRpbmcgc2VsZWN0aW9uXHJcbiAgICAgICAgc2V0UGVuZGluZ1NlbGVjdGlvbih1bmRlZmluZWQpXHJcbiAgICB9XHJcblxyXG4gICAgLy8gdGltZU9wdGlvbnMgcmVtb3ZlZCBhcyB3ZSdyZSBub3cgdXNpbmcgVGltZVBpY2tlciBjb21wb25lbnRcclxuXHJcbiAgICAvLyBEZXRlcm1pbmUgaWYgd2Ugc2hvdWxkIGluY2x1ZGUgdGltZSBiYXNlZCBvbiB0aGUgdHlwZSBwcm9wIG9yIHRoZSBkZXByZWNhdGVkIGluY2x1ZGVUaW1lIHByb3BcclxuICAgIGNvbnN0IFtzaG91bGRJbmNsdWRlVGltZSwgc2V0U2hvdWxkSW5jbHVkZVRpbWVdID0gdXNlU3RhdGUoXHJcbiAgICAgICAgdHlwZSA9PT0gJ2RhdGV0aW1lJyB8fCBpbmNsdWRlVGltZSxcclxuICAgIClcclxuXHJcbiAgICAvLyBVcGRhdGUgc2hvdWxkSW5jbHVkZVRpbWUgd2hlbiB0eXBlIG9yIGluY2x1ZGVUaW1lIGNoYW5nZXNcclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgc2V0U2hvdWxkSW5jbHVkZVRpbWUodHlwZSA9PT0gJ2RhdGV0aW1lJyB8fCBpbmNsdWRlVGltZSlcclxuICAgIH0sIFt0eXBlLCBpbmNsdWRlVGltZV0pXHJcblxyXG4gICAgY29uc3QgZGlzcGxheVRpbWVGb3JtYXQgPSBzaG91bGRJbmNsdWRlVGltZVxyXG4gICAgICAgID8gYCR7ZGF0ZUZvcm1hdH0gJHt0aW1lRm9ybWF0fWBcclxuICAgICAgICA6IGRhdGVGb3JtYXRcclxuXHJcbiAgICAvLyBHdWFyZCBhZ2FpbnN0IGludmFsaWQgZGF0ZXNcclxuICAgIGNvbnN0IGZvcm1hdERhdGVXaXRoVGltZSA9IChkYXRlOiBEYXRlIHwgdW5kZWZpbmVkKSA9PiB7XHJcbiAgICAgICAgaWYgKCFkYXRlKSByZXR1cm4gJydcclxuICAgICAgICBjb25zdCB2YWxpZERhdGUgPSBkYXRlIGluc3RhbmNlb2YgRGF0ZSA/IGRhdGUgOiBuZXcgRGF0ZSgpXHJcbiAgICAgICAgcmV0dXJuIGlzTmFOKHZhbGlkRGF0ZS5nZXRUaW1lKCkpXHJcbiAgICAgICAgICAgID8gJydcclxuICAgICAgICAgICAgOiBmb3JtYXQodmFsaWREYXRlLCBkaXNwbGF5VGltZUZvcm1hdClcclxuICAgIH1cclxuXHJcbiAgICAvLyBUaW1lIHBpY2tlciBpbXBsZW1lbnRhdGlvbiBub3cgdXNlcyB0aGUgVGltZVBpY2tlciBjb21wb25lbnQgd2l0aCBtb2RlIG9wdGlvblxyXG5cclxuICAgIGlmIChkaXNhYmxlZCkge1xyXG4gICAgICAgIC8vIFVzZSB0aGUgcHJvdmlkZWQgdmFsdWUgaWYgYXZhaWxhYmxlLCBvdGhlcndpc2UgdXNlIGN1cnJlbnQgZGF0ZVxyXG4gICAgICAgIGNvbnN0IGRpc3BsYXlEYXRlID0gKGRhdGU6IERhdGUgfCB1bmRlZmluZWQpID0+IHtcclxuICAgICAgICAgICAgaWYgKCFkYXRlIHx8ICEoZGF0ZSBpbnN0YW5jZW9mIERhdGUpIHx8IGlzTmFOKGRhdGUuZ2V0VGltZSgpKSkge1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIGZvcm1hdChuZXcgRGF0ZSgpLCBkYXRlRm9ybWF0KVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIHJldHVybiBmb3JtYXQoXHJcbiAgICAgICAgICAgICAgICBkYXRlLFxyXG4gICAgICAgICAgICAgICAgc2hvdWxkSW5jbHVkZVRpbWUgPyBkaXNwbGF5VGltZUZvcm1hdCA6IGRhdGVGb3JtYXQsXHJcbiAgICAgICAgICAgIClcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIEZvcm1hdCB0aGUgZGF0ZSBiYXNlZCBvbiB0aGUgbW9kZSBhbmQgdmFsdWVcclxuICAgICAgICBsZXQgZGlzcGxheVZhbHVlXHJcbiAgICAgICAgaWYgKG1vZGUgPT09ICdyYW5nZScpIHtcclxuICAgICAgICAgICAgY29uc3QgcmFuZ2UgPSBkYXRlVmFsdWUgYXMgRGF0ZVJhbmdlXHJcbiAgICAgICAgICAgIGlmIChyYW5nZT8uZnJvbSkge1xyXG4gICAgICAgICAgICAgICAgZGlzcGxheVZhbHVlID0gcmFuZ2UudG9cclxuICAgICAgICAgICAgICAgICAgICA/IGAke2Rpc3BsYXlEYXRlKHJhbmdlLmZyb20pfSAtICR7ZGlzcGxheURhdGUocmFuZ2UudG8pfWBcclxuICAgICAgICAgICAgICAgICAgICA6IGRpc3BsYXlEYXRlKHJhbmdlLmZyb20pXHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBjdXJyZW50Rm9ybWF0dGVkID0gZm9ybWF0KG5ldyBEYXRlKCksIGRhdGVGb3JtYXQpXHJcbiAgICAgICAgICAgICAgICBkaXNwbGF5VmFsdWUgPSBgJHtjdXJyZW50Rm9ybWF0dGVkfSAtICR7Y3VycmVudEZvcm1hdHRlZH1gXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBkaXNwbGF5VmFsdWUgPVxyXG4gICAgICAgICAgICAgICAgZGF0ZVZhbHVlIGluc3RhbmNlb2YgRGF0ZVxyXG4gICAgICAgICAgICAgICAgICAgID8gZGlzcGxheURhdGUoZGF0ZVZhbHVlIGFzIERhdGUpXHJcbiAgICAgICAgICAgICAgICAgICAgOiBmb3JtYXQobmV3IERhdGUoKSwgZGF0ZUZvcm1hdClcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICB7bGFiZWwgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgIDxMYWJlbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBhc0NoaWxkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiZGF0ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHBvc2l0aW9uPXtsYWJlbFBvc2l0aW9ufVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17ZGlzYWJsZWR9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7bGFiZWx9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cclxuICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHctZnVsbFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJkYXRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17ZGlzYWJsZWR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oJ3B4LTQganVzdGlmeS1zdGFydCB3LWZ1bGwnKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgaWNvbkxlZnQ9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWNvbiA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBSZWFjdC5pc1ZhbGlkRWxlbWVudChpY29uKSA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgUmVhY3QuY2xvbmVFbGVtZW50KFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWNvbiBhcyBSZWFjdC5SZWFjdEVsZW1lbnQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnbXItWzguNXB4XSB3LTUgaC01IHRleHQtbmV1dHJhbC00MDAnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFJlYWN0LmNyZWF0ZUVsZW1lbnQoaWNvbiBhcyBhbnksIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU6IDIwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdtci1bOC41cHhdIHctNSBoLTUgdGV4dC1uZXV0cmFsLTQwMCcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FsZW5kYXJJY29uIGNsYXNzTmFtZT1cIm1yLVs4LjVweF0gdy01IGgtNSB0ZXh0LW5ldXRyYWwtNDAwXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7Li4uYnV0dG9uUHJvcHN9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge2Rpc3BsYXlWYWx1ZX1cclxuICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICApXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgcmVuZGVyQnV0dG9uTGFiZWwgPSAoKSA9PiB7XHJcbiAgICAgICAgaWYgKG1vZGUgPT09ICdyYW5nZScpIHtcclxuICAgICAgICAgICAgY29uc3QgcmFuZ2UgPSBkYXRlVmFsdWUgYXMgRGF0ZVJhbmdlXHJcbiAgICAgICAgICAgIGlmIChyYW5nZT8uZnJvbSkge1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHJhbmdlLnRvID8gKFxyXG4gICAgICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXREYXRlV2l0aFRpbWUocmFuZ2UuZnJvbSl9IC17JyAnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0RGF0ZVdpdGhUaW1lKHJhbmdlLnRvKX1cclxuICAgICAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgZm9ybWF0RGF0ZVdpdGhUaW1lKHJhbmdlLmZyb20pXHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgLy8gVXNlIGNvbnNpc3RlbnQgZGF0ZSBmb3JtYXQgZm9yIHBsYWNlaG9sZGVyXHJcbiAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cclxuICAgICAgICAgICAgICAgICAgICB7YWN0dWFsUGxhY2Vob2xkZXJ9XHJcbiAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgIClcclxuICAgICAgICB9IGVsc2UgaWYgKGRhdGVWYWx1ZSkge1xyXG4gICAgICAgICAgICByZXR1cm4gZm9ybWF0RGF0ZVdpdGhUaW1lKGRhdGVWYWx1ZSBhcyBEYXRlKVxyXG4gICAgICAgIH1cclxuICAgICAgICAvLyBVc2UgY29uc2lzdGVudCBkYXRlIGZvcm1hdCBmb3IgcGxhY2Vob2xkZXJcclxuICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmRcIj57YWN0dWFsUGxhY2Vob2xkZXJ9PC9zcGFuPlxyXG4gICAgICAgIClcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gKFxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPXtjbih3cmFwcGVyQ2xhc3NOYW1lKX0+XHJcbiAgICAgICAgICAgIHtsYWJlbCAmJiAoXHJcbiAgICAgICAgICAgICAgICA8TGFiZWwgaWQ9XCJkYXRlXCIgcG9zaXRpb249e2xhYmVsUG9zaXRpb259IGRpc2FibGVkPXtkaXNhYmxlZH0+XHJcbiAgICAgICAgICAgICAgICAgICAge2xhYmVsfVxyXG4gICAgICAgICAgICAgICAgPC9MYWJlbD5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPFBvcG92ZXJcclxuICAgICAgICAgICAgICAgIG1vZGFsPXttb2RhbH1cclxuICAgICAgICAgICAgICAgIG9wZW49e29wZW59XHJcbiAgICAgICAgICAgICAgICBvbk9wZW5DaGFuZ2U9eyhpc09wZW4pID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAvLyBXaGVuIG9wZW5pbmcgdGhlIHBvcG92ZXIsIGluaXRpYWxpemUgcGVuZGluZ1NlbGVjdGlvbiB3aXRoIHRoZSBjdXJyZW50IHZhbHVlXHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKGlzT3BlbiAmJiAhcGVuZGluZ1NlbGVjdGlvbiAmJiBkYXRlVmFsdWUpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0UGVuZGluZ1NlbGVjdGlvbihkYXRlVmFsdWUpXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIHNldE9wZW4oaXNPcGVuKVxyXG4gICAgICAgICAgICAgICAgfX0+XHJcbiAgICAgICAgICAgICAgICA8UG9wb3ZlclRyaWdnZXIgYXNDaGlsZD5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHctZnVsbFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cImRhdGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2Rpc2FibGVkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWNvbkxlZnQ9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGljb24gPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFJlYWN0LmlzVmFsaWRFbGVtZW50KGljb24pID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWNvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgUmVhY3QuY3JlYXRlRWxlbWVudChpY29uIGFzIGFueSwge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU6IDIwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZTogJ3RleHQtbXV0ZWQtZm9yZWdyb3VuZCcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhbGVuZGFySWNvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT17MjB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LW5ldXRyYWwtNDAwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdweC00IGp1c3RpZnktc3RhcnQgdy1mdWxsJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGVhcmFibGUgJiYgJ3ByLTEwJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Li4uYnV0dG9uUHJvcHN9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cmVuZGVyQnV0dG9uTGFiZWwoKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtjbGVhcmFibGUgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIChkYXRlVmFsdWUgaW5zdGFuY2VvZiBEYXRlIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGRhdGVWYWx1ZSBhcyBEYXRlUmFuZ2UpPy5mcm9tKSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cImljb25cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSByaWdodC0xIHRvcC0xLzIgLXRyYW5zbGF0ZS15LTEvMiBoLTggdy04IHAtMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUNsZWFyfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhcmlhLWxhYmVsPVwiQ2xlYXIgZGF0ZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8WFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT17MTZ9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LW5ldXRyYWwtNDAwIGhvdmVyOnRleHQtYmFja2dyb3VuZDBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDwvUG9wb3ZlclRyaWdnZXI+XHJcbiAgICAgICAgICAgICAgICA8UG9wb3ZlckNvbnRlbnQgY2xhc3NOYW1lPVwidy1hdXRvIHAtMFwiIGFsaWduPVwic3RhcnRcIj5cclxuICAgICAgICAgICAgICAgICAgICB7bW9kZSA9PT0gJ3JhbmdlJyA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYWxlbmRhclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT1cInJhbmdlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhdXRvRm9jdXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtb2RlPVwicmFuZ2VcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1vbnRoPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGNvbmZpcm1TZWxlY3Rpb24gJiYgcGVuZGluZ1NlbGVjdGlvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAocGVuZGluZ1NlbGVjdGlvbiBhcyBEYXRlUmFuZ2UpPy5mcm9tXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IChkYXRlVmFsdWUgYXMgRGF0ZVJhbmdlKT8uZnJvbSkgfHxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbmV3IERhdGUoKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBhbGxvd0Z1dHVyZVllYXJzPXthbGxvd0Z1dHVyZVllYXJzfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIHNob3dZZWFyUGlja2VyPXtzaG93WWVhclBpY2tlcn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXB0aW9uTGF5b3V0PVwiZHJvcGRvd25cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uZmlybVNlbGVjdGlvbiAmJiBwZW5kaW5nU2VsZWN0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IChwZW5kaW5nU2VsZWN0aW9uIGFzIERhdGVSYW5nZSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogKGRhdGVWYWx1ZSBhcyBEYXRlUmFuZ2UpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uU2VsZWN0PXsodmFsdWUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gRW5zdXJlIHdlIG1haW50YWluIHRoZSBkYXRlIGZvcm1hdCB3aGVuIGEgZGF0ZSBpcyB1bnNlbGVjdGVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZVZhbHVlQ2hhbmdlKHZhbHVlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbnVtYmVyT2ZNb250aHM9e251bWJlck9mTW9udGhzfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNob3dXZWVrTnVtYmVyPXtzaG93V2Vla051bWJlcnN9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWxpZGF0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IChkYXRlKSA9PiAhdmFsaWRhdGVEYXRlKGRhdGUpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHVuZGVmaW5lZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c2hvdWxkSW5jbHVkZVRpbWUgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zIGJvcmRlci10IGJvcmRlci1ib3JkZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGltZVBpY2tlclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXsoKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBXaGVuIGNvbmZpcm1hdGlvbiBpcyBlbmFibGVkLCB1c2UgdGltZSBmcm9tIHBlbmRpbmdTZWxlY3Rpb24gaWYgYXZhaWxhYmxlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxldCBjdXJyZW50VGltZSA9IHRpbWVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uZmlybVNlbGVjdGlvbiAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBlbmRpbmdTZWxlY3Rpb24gYXMgRGF0ZVJhbmdlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApPy5mcm9tXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZnJvbURhdGUgPSAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGVuZGluZ1NlbGVjdGlvbiBhcyBEYXRlUmFuZ2VcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkuZnJvbSFcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnJlbnRUaW1lID0ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhvdXI6IGZyb21EYXRlLmdldEhvdXJzKCksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWludXRlOiBmcm9tRGF0ZS5nZXRNaW51dGVzKCksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChjdXJyZW50VGltZSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGUuc2V0SG91cnMoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3VycmVudFRpbWUuaG91cixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50VGltZS5taW51dGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGRhdGVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBJZiBubyB0aW1lIGlzIHNldCwgdXNlIGN1cnJlbnQgdGltZSBhbmQgc3luYyBzdGF0ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld1RpbWUgPSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBob3VyOiBub3cuZ2V0SG91cnMoKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pbnV0ZTogbm93LmdldE1pbnV0ZXMoKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRUaW1lKG5ld1RpbWUpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBub3dcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSgpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRvVmFsdWU9eygoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIFdoZW4gY29uZmlybWF0aW9uIGlzIGVuYWJsZWQsIHVzZSB0aW1lIGZyb20gcGVuZGluZ1NlbGVjdGlvbiBpZiBhdmFpbGFibGVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGV0IGN1cnJlbnRUb1RpbWUgPSB0b1RpbWVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uZmlybVNlbGVjdGlvbiAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBlbmRpbmdTZWxlY3Rpb24gYXMgRGF0ZVJhbmdlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApPy50b1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHRvRGF0ZSA9IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwZW5kaW5nU2VsZWN0aW9uIGFzIERhdGVSYW5nZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKS50byFcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnJlbnRUb1RpbWUgPSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaG91cjogdG9EYXRlLmdldEhvdXJzKCksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWludXRlOiB0b0RhdGUuZ2V0TWludXRlcygpLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoY3VycmVudFRvVGltZSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGUuc2V0SG91cnMoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3VycmVudFRvVGltZS5ob3VyLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnJlbnRUb1RpbWUubWludXRlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBkYXRlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHVuZGVmaW5lZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pKCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZVRpbWVDaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25Ub0NoYW5nZT17KGRhdGUpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZVRpbWVDaGFuZ2UoZGF0ZSwgdHJ1ZSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gQWx3YXlzIGVuYWJsZSB0aW1lIHBpY2tlciByZWdhcmRsZXNzIG9mIHdoZXRoZXIgYSBkYXRlIGlzIHNlbGVjdGVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2ZhbHNlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbW9kZT17dGltZU1vZGV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aW1lTW9kZSA9PT0gJ3JhbmdlJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnVGltZSBSYW5nZSdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogdW5kZWZpbmVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2NvbmZpcm1TZWxlY3Rpb24gJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZXBhcmF0b3IgY2xhc3NOYW1lPVwibXktMFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ3AtMyBmbGV4IGdhcC0zIGp1c3RpZnktZW5kJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2NsZWFyYWJsZSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZpdFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVDbGVhckluUG9wb3ZlclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWNvbkxlZnQ9e1h9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZpdFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhcmlhLWxhYmVsPVwiQ2xlYXIgZGF0ZSByYW5nZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQ2xlYXJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUNvbmZpcm19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFwZW5kaW5nU2VsZWN0aW9ufVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGljb25MZWZ0PXtDaGVja30+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2NvbmZpcm1CdXR0b25UZXh0fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYWxlbmRhclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT1cInNpbmdsZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXV0b0ZvY3VzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbW9kZT1cInNpbmdsZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbW9udGg9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoY29uZmlybVNlbGVjdGlvbiAmJiBwZW5kaW5nU2VsZWN0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IChwZW5kaW5nU2VsZWN0aW9uIGFzIERhdGUpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IChkYXRlVmFsdWUgYXMgRGF0ZSkpIHx8IG5ldyBEYXRlKClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWQ9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25maXJtU2VsZWN0aW9uICYmIHBlbmRpbmdTZWxlY3Rpb25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gKHBlbmRpbmdTZWxlY3Rpb24gYXMgRGF0ZSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogKGRhdGVWYWx1ZSBhcyBEYXRlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvblNlbGVjdD17KHZhbHVlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIEVuc3VyZSB3ZSBtYWludGFpbiB0aGUgZGF0ZSBmb3JtYXQgd2hlbiBhIGRhdGUgaXMgdW5zZWxlY3RlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVWYWx1ZUNoYW5nZSh2YWx1ZSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhcHRpb25MYXlvdXQ9XCJkcm9wZG93blwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbnVtYmVyT2ZNb250aHM9e251bWJlck9mTW9udGhzfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNob3dXZWVrTnVtYmVyPXtzaG93V2Vla051bWJlcnN9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWxpZGF0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IChkYXRlKSA9PiAhdmFsaWRhdGVEYXRlKGRhdGUpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHVuZGVmaW5lZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c2hvdWxkSW5jbHVkZVRpbWUgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zIGJvcmRlci10IGJvcmRlci1ib3JkZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGltZVBpY2tlclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXsoKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBXaGVuIGNvbmZpcm1hdGlvbiBpcyBlbmFibGVkLCB1c2UgdGltZSBmcm9tIHBlbmRpbmdTZWxlY3Rpb24gaWYgYXZhaWxhYmxlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxldCBjdXJyZW50VGltZSA9IHRpbWVcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbmZpcm1TZWxlY3Rpb24gJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBlbmRpbmdTZWxlY3Rpb24gaW5zdGFuY2VvZlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIERhdGUgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICFpc05hTihcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwZW5kaW5nU2VsZWN0aW9uLmdldFRpbWUoKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50VGltZSA9IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBob3VyOiBwZW5kaW5nU2VsZWN0aW9uLmdldEhvdXJzKCksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWludXRlOiBwZW5kaW5nU2VsZWN0aW9uLmdldE1pbnV0ZXMoKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGN1cnJlbnRUaW1lKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0ZS5zZXRIb3VycyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50VGltZS5ob3VyLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnJlbnRUaW1lLm1pbnV0ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gZGF0ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB1bmRlZmluZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSgpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRvVmFsdWU9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0b1RpbWVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gKCgpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGRhdGUgPVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5ldyBEYXRlKClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGUuc2V0SG91cnMoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdG9UaW1lLmhvdXIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdG9UaW1lLm1pbnV0ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gZGF0ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSgpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHVuZGVmaW5lZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlVGltZUNoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvblRvQ2hhbmdlPXsoZGF0ZSkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlVGltZUNoYW5nZShkYXRlLCB0cnVlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBBbHdheXMgZW5hYmxlIHRpbWUgcGlja2VyIHJlZ2FyZGxlc3Mgb2Ygd2hldGhlciBhIGRhdGUgaXMgc2VsZWN0ZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17ZmFsc2V9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtb2RlPXt0aW1lTW9kZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbD17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpbWVNb2RlID09PSAncmFuZ2UnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdUaW1lIFJhbmdlJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiB1bmRlZmluZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y29uZmlybVNlbGVjdGlvbiAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlcGFyYXRvciBjbGFzc05hbWU9XCJteS0wXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAncC0zIGZsZXggZ2FwLTMganVzdGlmeS1lbmQnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y2xlYXJhYmxlICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZml0XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUNsZWFySW5Qb3BvdmVyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpY29uTGVmdD17WH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJDbGVhciBkYXRlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBDbGVhclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQ29uZmlybX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17IXBlbmRpbmdTZWxlY3Rpb259XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xlYXJhYmxlID8gJycgOiAndy1mdWxsJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y29uZmlybUJ1dHRvblRleHR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgPC9Qb3BvdmVyQ29udGVudD5cclxuICAgICAgICAgICAgPC9Qb3BvdmVyPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgKVxyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBEYXRlUGlja2VyXHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiQnV0dG9uIiwiQ2FsZW5kYXIiLCJQb3BvdmVyIiwiUG9wb3ZlckNvbnRlbnQiLCJQb3BvdmVyVHJpZ2dlciIsIkNhbGVuZGFySWNvbiIsIlgiLCJDaGVjayIsIlRpbWVQaWNrZXIiLCJjbiIsImZvcm1hdCIsImlzQWZ0ZXIiLCJpc0JlZm9yZSIsInNldCIsIkxhYmVsIiwiU2VwYXJhdG9yIiwiRGF0ZVBpY2tlciIsIm9uQ2hhbmdlIiwiY2xhc3NOYW1lIiwicGxhY2Vob2xkZXIiLCJtb2RlIiwidHlwZSIsImRpc2FibGVkIiwidmFsdWUiLCJkYXRlRm9ybWF0IiwidmFsaWRhdGlvbiIsIm51bWJlck9mTW9udGhzIiwiY2xvc2VPblNlbGVjdCIsInNob3dXZWVrTnVtYmVycyIsImluY2x1ZGVUaW1lIiwidGltZU1vZGUiLCJ0aW1lRm9ybWF0IiwidGltZUludGVydmFsIiwibGFiZWwiLCJsYWJlbFBvc2l0aW9uIiwiY2xlYXJhYmxlIiwiaWNvbiIsImNvbmZpcm1TZWxlY3Rpb24iLCJjb25maXJtQnV0dG9uVGV4dCIsIm1vZGFsIiwid3JhcHBlckNsYXNzTmFtZSIsImJ1dHRvblByb3BzIiwiZGF0ZVZhbHVlIiwic2V0RGF0ZVZhbHVlIiwiZnJvbSIsInVuZGVmaW5lZCIsInRvIiwib3BlbiIsInNldE9wZW4iLCJ0aW1lIiwic2V0VGltZSIsInRvVGltZSIsInNldFRvVGltZSIsInBlbmRpbmdTZWxlY3Rpb24iLCJzZXRQZW5kaW5nU2VsZWN0aW9uIiwiYWN0dWFsUGxhY2Vob2xkZXIiLCJpc0VxdWFsIiwiYSIsImIiLCJEYXRlIiwiZ2V0VGltZSIsImZyb21FcXVhbCIsInRvRXF1YWwiLCJob3VycyIsImdldEhvdXJzIiwibWludXRlcyIsImdldE1pbnV0ZXMiLCJob3VyIiwibWludXRlIiwiZnJvbUhvdXJzIiwiZnJvbU1pbnV0ZXMiLCJ0b0hvdXJzIiwidG9NaW51dGVzIiwidmFsaWRhdGVEYXRlIiwiZGF0ZSIsIm1pbkRhdGUiLCJtYXhEYXRlIiwiZGlzYWJsZWREYXRlcyIsImRpc2FibGVkRGF5c09mV2VlayIsInNvbWUiLCJkaXNhYmxlZERhdGUiLCJpbmNsdWRlcyIsImdldERheSIsImFwcGx5VGltZSIsInQiLCJzZWNvbmRzIiwibWlsbGlzZWNvbmRzIiwiaGFuZGxlVmFsdWVDaGFuZ2UiLCJuZXdWYWx1ZSIsImN1cnJlbnRUaW1lIiwic2hvdWxkSW5jbHVkZVRpbWUiLCJub3ciLCJjdXJyZW50VG9UaW1lIiwic3RhcnREYXRlIiwiZW5kRGF0ZSIsInNpbmdsZURhdGUiLCJyZXN1bHQiLCJoYW5kbGVUaW1lQ2hhbmdlIiwiaXNUb1RpbWUiLCJuZXdUaW1lIiwibmV3RW5kRGF0ZSIsIm5ld1N0YXJ0RGF0ZSIsIm5ld0RhdGUiLCJoYW5kbGVDbGVhciIsImUiLCJzdG9wUHJvcGFnYXRpb24iLCJoYW5kbGVDbGVhckluUG9wb3ZlciIsImhhbmRsZUNvbmZpcm0iLCJzZXRTaG91bGRJbmNsdWRlVGltZSIsImRpc3BsYXlUaW1lRm9ybWF0IiwiZm9ybWF0RGF0ZVdpdGhUaW1lIiwidmFsaWREYXRlIiwiaXNOYU4iLCJkaXNwbGF5RGF0ZSIsImRpc3BsYXlWYWx1ZSIsInJhbmdlIiwiY3VycmVudEZvcm1hdHRlZCIsImRpdiIsImFzQ2hpbGQiLCJpZCIsInBvc2l0aW9uIiwidmFyaWFudCIsImljb25MZWZ0IiwiaXNWYWxpZEVsZW1lbnQiLCJjbG9uZUVsZW1lbnQiLCJjcmVhdGVFbGVtZW50Iiwic2l6ZSIsInJlbmRlckJ1dHRvbkxhYmVsIiwic3BhbiIsIm9uT3BlbkNoYW5nZSIsImlzT3BlbiIsIm9uQ2xpY2siLCJhcmlhLWxhYmVsIiwiYWxpZ24iLCJhdXRvRm9jdXMiLCJtb250aCIsImNhcHRpb25MYXlvdXQiLCJzZWxlY3RlZCIsIm9uU2VsZWN0Iiwic2hvd1dlZWtOdW1iZXIiLCJmcm9tRGF0ZSIsInNldEhvdXJzIiwidG9WYWx1ZSIsInRvRGF0ZSIsIm9uVG9DaGFuZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DateRange.tsx\n"));

/***/ })

});