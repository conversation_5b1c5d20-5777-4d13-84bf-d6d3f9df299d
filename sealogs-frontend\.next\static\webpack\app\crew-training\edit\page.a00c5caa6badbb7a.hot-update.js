"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/edit/page",{

/***/ "(app-pages-browser)/./src/components/DateRange.tsx":
/*!**************************************!*\
  !*** ./src/components/DateRange.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui_time_picker__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/time-picker */ \"(app-pages-browser)/./src/components/ui/time-picker.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/isBefore.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/isAfter.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/set.mjs\");\n/* harmony import */ var _ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _ui_separator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst DatePicker = (param)=>{\n    let { onChange, className, placeholder = \"Select date\", mode = \"range\", type = \"date\", disabled = false, value, dateFormat = \"dd LLLL, y\", validation, numberOfMonths = mode === \"range\" ? 2 : 1, closeOnSelect = true, showWeekNumbers = false, includeTime = false, timeMode = \"single\", timeFormat = \"HH:mm\", timeInterval = 30, label, labelPosition = \"top\", clearable = false, icon, confirmSelection = true, confirmButtonText = \"Confirm\", modal = false, wrapperClassName = \"\", ...buttonProps } = param;\n    var _this = undefined;\n    _s();\n    const [dateValue, setDateValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value || (mode === \"range\" ? {\n        from: undefined,\n        to: undefined\n    } : undefined));\n    // We'll use buttonProps directly but ensure we don't pass our custom type prop to the Button component\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [time, setTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [toTime, setToTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State to track pending selection when confirmation button is enabled\n    const [pendingSelection, setPendingSelection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value || undefined);\n    // Set placeholder based on mode\n    const actualPlaceholder = mode === \"range\" ? \"Select date range\" : \"Select date\";\n    // Optimized deep equality check for dates\n    const isDateEqual = (a, b)=>{\n        if (a === b) return true;\n        if (!a || !b) return false;\n        return a instanceof Date && b instanceof Date && a.getTime() === b.getTime();\n    };\n    const isValueEqual = (a, b)=>{\n        if (a === b) return true;\n        if (a instanceof Date && b instanceof Date) return isDateEqual(a, b);\n        if (a && b && typeof a === \"object\" && typeof b === \"object\" && \"from\" in a && \"to\" in a && \"from\" in b && \"to\" in b) {\n            return isDateEqual(a.from, b.from) && isDateEqual(a.to, b.to);\n        }\n        return false;\n    };\n    // Helper functions for time initialization\n    const getCurrentTime = ()=>{\n        const now = new Date();\n        return {\n            hour: now.getHours(),\n            minute: now.getMinutes()\n        };\n    };\n    const getTimeFromDate = (date)=>({\n            hour: date.getHours(),\n            minute: date.getMinutes()\n        });\n    const initializeTime = (existingTime, date)=>{\n        if (existingTime) return existingTime;\n        if (date && (date.getHours() || date.getMinutes())) return getTimeFromDate(date);\n        return shouldIncludeTime ? getCurrentTime() : null;\n    };\n    // Helper function to reset date value based on mode\n    const getEmptyDateValue = ()=>mode === \"range\" ? {\n            from: undefined,\n            to: undefined\n        } : undefined;\n    // Helper function to clear all date and time state\n    const clearAllState = ()=>{\n        setDateValue(getEmptyDateValue());\n        setPendingSelection(undefined);\n        setTime(null);\n        setToTime(null);\n        onChange(null);\n    };\n    // Helper function to render icon consistently\n    const renderIcon = function() {\n        let className = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"text-muted-foreground\";\n        if (!icon) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            size: 20,\n            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"text-neutral-400\", className)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 215,\n            columnNumber: 17\n        }, _this);\n        if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(icon)) {\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(icon, {\n                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-5 h-5\", className)\n            });\n        }\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(icon, {\n            size: 20,\n            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(className)\n        });\n    };\n    // Helper function to create Date from time object\n    const createDateFromTime = (timeObj)=>{\n        const date = new Date();\n        date.setHours(timeObj.hour, timeObj.minute, 0, 0);\n        return date;\n    };\n    // Helper function to get time picker value with confirmation logic\n    const getTimePickerValue = (currentTime, pendingDate, setTimeCallback)=>{\n        let resolvedTime = currentTime;\n        // Use time from pending selection if confirmation is enabled and meaningful\n        if (confirmSelection && pendingDate && !isNaN(pendingDate.getTime())) {\n            const pendingHours = pendingDate.getHours();\n            const pendingMinutes = pendingDate.getMinutes();\n            if (pendingHours !== 0 || pendingMinutes !== 0 || !currentTime) {\n                resolvedTime = {\n                    hour: pendingHours,\n                    minute: pendingMinutes\n                };\n            }\n        }\n        if (resolvedTime) {\n            return createDateFromTime(resolvedTime);\n        }\n        // Fallback to current time and sync state\n        const now = new Date();\n        const newTime = {\n            hour: now.getHours(),\n            minute: now.getMinutes()\n        };\n        if (setTimeCallback) setTimeCallback(newTime);\n        return now;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only update if the value has actually changed\n        if (!isValueEqual(value, dateValue)) {\n            if (value) {\n                setDateValue(value);\n                // Also update pendingSelection with the value\n                setPendingSelection(value);\n                if (value instanceof Date) {\n                    const timeFromDate = initializeTime(null, value);\n                    if (timeFromDate) setTime(timeFromDate);\n                } else if (\"from\" in value && value.from instanceof Date) {\n                    const { from, to } = value;\n                    const fromTime = initializeTime(null, from);\n                    if (fromTime) setTime(fromTime);\n                    if (to instanceof Date) {\n                        const toTimeFromDate = initializeTime(null, to);\n                        if (toTimeFromDate) setToTime(toTimeFromDate);\n                    }\n                }\n            } else {\n                // When value is null/undefined, reset to initial state but maintain format\n                setDateValue(getEmptyDateValue());\n                setPendingSelection(undefined);\n            }\n        }\n    }, [\n        value,\n        mode\n    ]);\n    const validateDate = (date)=>{\n        if (!validation) return true;\n        const { minDate, maxDate, disabledDates, disabledDaysOfWeek } = validation;\n        if (minDate && (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_10__.isBefore)(date, minDate)) return false;\n        if (maxDate && (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.isAfter)(date, maxDate)) return false;\n        if (disabledDates === null || disabledDates === void 0 ? void 0 : disabledDates.some((disabledDate)=>(0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(disabledDate, \"yyyy-MM-dd\") === (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(date, \"yyyy-MM-dd\"))) return false;\n        if (disabledDaysOfWeek === null || disabledDaysOfWeek === void 0 ? void 0 : disabledDaysOfWeek.includes(date.getDay())) return false;\n        return true;\n    };\n    const applyTime = (date, t)=>date && t ? (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.set)(date, {\n            hours: t.hour,\n            minutes: t.minute,\n            seconds: 0,\n            milliseconds: 0\n        }) : date;\n    const handleValueChange = (newValue)=>{\n        if (!newValue) {\n            // When a date is unselected, maintain the format consistency\n            setDateValue(getEmptyDateValue());\n            setPendingSelection(undefined);\n            onChange(null);\n            return;\n        }\n        if (mode === \"range\") {\n            const { from, to } = newValue;\n            if (from && !validateDate(from)) return;\n            if (to && !validateDate(to)) return;\n            // If confirmation is required, store the selection in pending state\n            if (confirmSelection) {\n                setPendingSelection({\n                    from,\n                    to\n                });\n            } else {\n                finalizeSelection({\n                    from,\n                    to\n                });\n            }\n        } else {\n            const singleDate = newValue;\n            if (!validateDate(singleDate)) return;\n            // If confirmation is required, store the selection in pending state\n            if (confirmSelection) {\n                setPendingSelection(singleDate);\n            } else {\n                setDateValue(singleDate);\n                // Initialize time preserving existing time or using date value\n                const existingDate = dateValue instanceof Date ? dateValue : undefined;\n                const currentTime = initializeTime(time, existingDate);\n                if (currentTime && !time) setTime(currentTime);\n                // Always apply time if shouldIncludeTime is true\n                const result = shouldIncludeTime ? applyTime(singleDate, currentTime) : singleDate;\n                onChange(result);\n                if (closeOnSelect && !shouldIncludeTime) setOpen(false);\n            }\n        }\n    };\n    const handleTimeChange = function(date) {\n        let isToTime = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!date) return;\n        const newTime = {\n            hour: date.getHours(),\n            minute: date.getMinutes()\n        };\n        // Check if the time has actually changed before updating state\n        if (isToTime) {\n            const currentToTime = toTime;\n            if (!currentToTime || currentToTime.hour !== newTime.hour || currentToTime.minute !== newTime.minute) {\n                setToTime(newTime);\n                // Update pendingSelection with the new time\n                if (confirmSelection && (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.to)) {\n                    const newEndDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.set)(pendingSelection.to, {\n                        hours: newTime.hour,\n                        minutes: newTime.minute,\n                        seconds: 0,\n                        milliseconds: 0\n                    });\n                    setPendingSelection({\n                        ...pendingSelection,\n                        to: newEndDate\n                    });\n                }\n                // If confirmation is not required, call onChange directly\n                if (!confirmSelection) {\n                    if (dateValue === null || dateValue === void 0 ? void 0 : dateValue.to) {\n                        const newEndDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.set)(dateValue.to, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange({\n                            startDate: dateValue.from,\n                            endDate: newEndDate\n                        });\n                    }\n                }\n            }\n        } else {\n            const currentTime = time;\n            if (!currentTime || currentTime.hour !== newTime.hour || currentTime.minute !== newTime.minute) {\n                setTime(newTime);\n                // Update pendingSelection with the new time\n                if (confirmSelection) {\n                    if (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from) {\n                        const newStartDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.set)(pendingSelection.from, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        setPendingSelection({\n                            ...pendingSelection,\n                            from: newStartDate\n                        });\n                    } else if (pendingSelection instanceof Date) {\n                        const newDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.set)(pendingSelection, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        setPendingSelection(newDate);\n                    }\n                }\n                // If confirmation is not required, call onChange directly\n                if (!confirmSelection) {\n                    if (dateValue === null || dateValue === void 0 ? void 0 : dateValue.from) {\n                        const newStartDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.set)(dateValue.from, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange({\n                            startDate: newStartDate,\n                            endDate: dateValue.to\n                        });\n                    } else if (dateValue instanceof Date) {\n                        const newDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.set)(dateValue, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange(newDate);\n                    }\n                }\n            }\n        }\n    };\n    const handleClear = (e)=>{\n        e.stopPropagation() // Prevent triggering the popover\n        ;\n        clearAllState();\n    };\n    // Function to handle clear button click inside the popover\n    const handleClearInPopover = ()=>{\n        clearAllState();\n        setOpen(false) // Close the popover after clearing\n        ;\n    };\n    // Helper function to finalize selection (used by both direct selection and confirmation)\n    const finalizeSelection = function(selection) {\n        let closePopover = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (mode === \"range\") {\n            const { from, to } = selection;\n            setDateValue({\n                from,\n                to\n            });\n            const currentTime = initializeTime(time, from);\n            if (currentTime && !time) setTime(currentTime);\n            const currentToTime = initializeTime(toTime, to);\n            if (currentToTime && !toTime) setToTime(currentToTime);\n            onChange({\n                startDate: applyTime(from, currentTime),\n                endDate: applyTime(to, currentToTime)\n            });\n        } else {\n            const singleDate = selection;\n            setDateValue(singleDate);\n            const existingDate = dateValue instanceof Date ? dateValue : undefined;\n            const currentTime = initializeTime(time, existingDate);\n            if (currentTime && !time) setTime(currentTime);\n            const result = shouldIncludeTime ? applyTime(singleDate, currentTime) : singleDate;\n            onChange(result);\n        }\n        if (closePopover) {\n            setOpen(false);\n            setPendingSelection(undefined);\n        }\n    };\n    // Function to handle confirmation button click\n    const handleConfirm = ()=>{\n        if (!pendingSelection) return;\n        finalizeSelection(pendingSelection, true);\n    };\n    // timeOptions removed as we're now using TimePicker component\n    // Determine if we should include time based on the type prop or the deprecated includeTime prop\n    const [shouldIncludeTime, setShouldIncludeTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(type === \"datetime\" || includeTime);\n    // Update shouldIncludeTime when type or includeTime changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShouldIncludeTime(type === \"datetime\" || includeTime);\n    }, [\n        type,\n        includeTime\n    ]);\n    const displayTimeFormat = shouldIncludeTime ? \"\".concat(dateFormat, \" \").concat(timeFormat) : dateFormat;\n    // Guard against invalid dates\n    const formatDateWithTime = (date)=>{\n        if (!date) return \"\";\n        const validDate = date instanceof Date ? date : new Date();\n        return isNaN(validDate.getTime()) ? \"\" : (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(validDate, displayTimeFormat);\n    };\n    // Time picker implementation now uses the TimePicker component with mode option\n    if (disabled) {\n        // Use the provided value if available, otherwise use current date\n        const displayDate = (date)=>{\n            if (!date || !(date instanceof Date) || isNaN(date.getTime())) {\n                return (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(new Date(), dateFormat);\n            }\n            return (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(date, shouldIncludeTime ? displayTimeFormat : dateFormat);\n        };\n        // Format the date based on the mode and value\n        let displayValue;\n        if (mode === \"range\") {\n            const range = dateValue;\n            if (range === null || range === void 0 ? void 0 : range.from) {\n                displayValue = range.to ? \"\".concat(displayDate(range.from), \" - \").concat(displayDate(range.to)) : displayDate(range.from);\n            } else {\n                const currentFormatted = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(new Date(), dateFormat);\n                displayValue = \"\".concat(currentFormatted, \" - \").concat(currentFormatted);\n            }\n        } else {\n            displayValue = dateValue instanceof Date ? displayDate(dateValue) : (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(new Date(), dateFormat);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                    asChild: true,\n                    id: \"date\",\n                    position: labelPosition,\n                    disabled: disabled,\n                    children: label\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                    lineNumber: 616,\n                    columnNumber: 21\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        id: \"date\",\n                        variant: \"outline\",\n                        disabled: disabled,\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-4 justify-start w-full\"),\n                        iconLeft: renderIcon(\"mr-[8.5px] w-5 h-5 text-neutral-400\"),\n                        ...buttonProps,\n                        type: \"button\",\n                        children: displayValue\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 625,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                    lineNumber: 624,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 614,\n            columnNumber: 13\n        }, undefined);\n    }\n    const renderButtonLabel = ()=>{\n        if (mode === \"range\") {\n            const range = dateValue;\n            if (range === null || range === void 0 ? void 0 : range.from) {\n                return range.to ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        formatDateWithTime(range.from),\n                        \" -\",\n                        \" \",\n                        formatDateWithTime(range.to)\n                    ]\n                }, void 0, true) : formatDateWithTime(range.from);\n            }\n            // Use consistent date format for placeholder\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-muted-foreground\",\n                children: actualPlaceholder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 657,\n                columnNumber: 17\n            }, undefined);\n        } else if (dateValue) {\n            return formatDateWithTime(dateValue);\n        }\n        // Use consistent date format for placeholder\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-muted-foreground\",\n            children: actualPlaceholder\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 666,\n            columnNumber: 13\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(wrapperClassName),\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                id: \"date\",\n                position: labelPosition,\n                disabled: disabled,\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 673,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.Popover, {\n                modal: modal,\n                open: open,\n                onOpenChange: (isOpen)=>{\n                    // When opening the popover, initialize pendingSelection with the current value\n                    if (isOpen && !pendingSelection && dateValue) {\n                        setPendingSelection(dateValue);\n                    }\n                    setOpen(isOpen);\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.PopoverTrigger, {\n                        asChild: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    id: \"date\",\n                                    variant: \"outline\",\n                                    disabled: disabled,\n                                    iconLeft: renderIcon(),\n                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-4 justify-start w-full\", clearable && \"pr-10\"),\n                                    ...buttonProps,\n                                    type: \"button\",\n                                    children: renderButtonLabel()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 689,\n                                    columnNumber: 25\n                                }, undefined),\n                                clearable && (dateValue instanceof Date || (dateValue === null || dateValue === void 0 ? void 0 : dateValue.from)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"button\",\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    className: \"absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0\",\n                                    onClick: handleClear,\n                                    \"aria-label\": \"Clear date\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        size: 16,\n                                        className: \"text-neutral-400 hover:text-background0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 712,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 705,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 688,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 687,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.PopoverContent, {\n                        className: \"w-auto p-0\",\n                        align: \"start\",\n                        children: mode === \"range\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__.Calendar, {\n                                    autoFocus: true,\n                                    mode: \"range\",\n                                    month: (confirmSelection && pendingSelection ? pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from : dateValue === null || dateValue === void 0 ? void 0 : dateValue.from) || new Date(),\n                                    // allowFutureYears={allowFutureYears}\n                                    // showYearPicker={showYearPicker}\n                                    captionLayout: \"dropdown\",\n                                    selected: confirmSelection && pendingSelection ? pendingSelection : dateValue,\n                                    onSelect: (value)=>{\n                                        // Ensure we maintain the date format when a date is unselected\n                                        handleValueChange(value);\n                                    },\n                                    numberOfMonths: numberOfMonths,\n                                    showWeekNumber: showWeekNumbers,\n                                    disabled: validation ? (date)=>!validateDate(date) : undefined\n                                }, \"range\", false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 723,\n                                    columnNumber: 29\n                                }, undefined),\n                                shouldIncludeTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-t border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_time_picker__WEBPACK_IMPORTED_MODULE_5__.TimePicker, {\n                                            value: getTimePickerValue(time, pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from, setTime),\n                                            toValue: getTimePickerValue(toTime, pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.to, setToTime),\n                                            onChange: handleTimeChange,\n                                            onToChange: (date)=>handleTimeChange(date, true),\n                                            // Always enable time picker regardless of whether a date is selected\n                                            disabled: false,\n                                            className: \"w-full\",\n                                            mode: timeMode,\n                                            label: timeMode === \"range\" ? \"Time Range\" : undefined\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 756,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 755,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 754,\n                                    columnNumber: 33\n                                }, undefined),\n                                confirmSelection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                                            className: \"my-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 788,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-3 flex gap-3 justify-end\"),\n                                            children: [\n                                                clearable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-fit\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: handleClearInPopover,\n                                                        iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                                        className: \"w-fit\",\n                                                        \"aria-label\": \"Clear date range\",\n                                                        children: \"Clear\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                        lineNumber: 795,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 794,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: handleConfirm,\n                                                    disabled: !pendingSelection,\n                                                    iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                                    children: confirmButtonText\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 807,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 789,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 722,\n                            columnNumber: 25\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__.Calendar, {\n                                    autoFocus: true,\n                                    mode: \"single\",\n                                    month: (confirmSelection && pendingSelection ? pendingSelection : dateValue) || new Date(),\n                                    selected: confirmSelection && pendingSelection ? pendingSelection : dateValue,\n                                    onSelect: (value)=>{\n                                        // Ensure we maintain the date format when a date is unselected\n                                        handleValueChange(value);\n                                    },\n                                    captionLayout: \"dropdown\",\n                                    numberOfMonths: numberOfMonths,\n                                    showWeekNumber: showWeekNumbers,\n                                    disabled: validation ? (date)=>!validateDate(date) : undefined\n                                }, \"single\", false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 819,\n                                    columnNumber: 29\n                                }, undefined),\n                                shouldIncludeTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-t border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_time_picker__WEBPACK_IMPORTED_MODULE_5__.TimePicker, {\n                                            value: getTimePickerValue(time, pendingSelection instanceof Date ? pendingSelection : undefined, setTime),\n                                            toValue: getTimePickerValue(toTime, undefined, setToTime),\n                                            onChange: handleTimeChange,\n                                            onToChange: (date)=>handleTimeChange(date, true),\n                                            // Always enable time picker regardless of whether a date is selected\n                                            disabled: false,\n                                            className: \"w-full\",\n                                            mode: timeMode,\n                                            label: timeMode === \"range\" ? \"Time Range\" : undefined\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 849,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 848,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 847,\n                                    columnNumber: 33\n                                }, undefined),\n                                confirmSelection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                                            className: \"my-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 881,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-3 flex gap-3 justify-end\"),\n                                            children: [\n                                                clearable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-fit\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: handleClearInPopover,\n                                                        iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                                        \"aria-label\": \"Clear date\",\n                                                        children: \"Clear\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                        lineNumber: 888,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 887,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: handleConfirm,\n                                                    disabled: !pendingSelection,\n                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(clearable ? \"\" : \"w-full\"),\n                                                    children: confirmButtonText\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 899,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 882,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 818,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 720,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 677,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n        lineNumber: 671,\n        columnNumber: 9\n    }, undefined);\n};\n_s(DatePicker, \"EPGvag/dmevMVfeSbcbdVrI0y2w=\");\n_c = DatePicker;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DatePicker);\nvar _c;\n$RefreshReg$(_c, \"DatePicker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DateRange.tsx\n"));

/***/ })

});