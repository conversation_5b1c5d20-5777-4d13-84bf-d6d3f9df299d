"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/vessel/info/page",{

/***/ "(app-pages-browser)/./src/components/DateRange.tsx":
/*!**************************************!*\
  !*** ./src/components/DateRange.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui_time_picker__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/time-picker */ \"(app-pages-browser)/./src/components/ui/time-picker.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/isBefore.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/isAfter.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/set.mjs\");\n/* harmony import */ var _ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _ui_separator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst DatePicker = (param)=>{\n    let { onChange, className, placeholder = \"Select date\", mode = \"range\", type = \"date\", disabled = false, value, dateFormat = \"dd LLLL, y\", validation, numberOfMonths = mode === \"range\" ? 2 : 1, closeOnSelect = true, showWeekNumbers = false, includeTime = false, timeMode = \"single\", timeFormat = \"HH:mm\", timeInterval = 30, label, labelPosition = \"top\", clearable = false, icon, confirmSelection = true, confirmButtonText = \"Confirm\", modal = false, wrapperClassName = \"\", ...buttonProps } = param;\n    var _this = undefined;\n    _s();\n    const [dateValue, setDateValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value || (mode === \"range\" ? {\n        from: undefined,\n        to: undefined\n    } : undefined));\n    // We'll use buttonProps directly but ensure we don't pass our custom type prop to the Button component\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [time, setTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [toTime, setToTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State to track pending selection when confirmation button is enabled\n    const [pendingSelection, setPendingSelection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value || undefined);\n    // Set placeholder based on mode\n    const actualPlaceholder = mode === \"range\" ? \"Select date range\" : \"Select date\";\n    // Optimized deep equality check for dates\n    const isDateEqual = (a, b)=>{\n        if (a === b) return true;\n        if (!a || !b) return false;\n        return a instanceof Date && b instanceof Date && a.getTime() === b.getTime();\n    };\n    const isValueEqual = (a, b)=>{\n        if (a === b) return true;\n        if (a instanceof Date && b instanceof Date) return isDateEqual(a, b);\n        if (a && b && typeof a === \"object\" && typeof b === \"object\" && \"from\" in a && \"to\" in a && \"from\" in b && \"to\" in b) {\n            return isDateEqual(a.from, b.from) && isDateEqual(a.to, b.to);\n        }\n        return false;\n    };\n    // Helper functions for time initialization\n    const getCurrentTime = ()=>{\n        const now = new Date();\n        return {\n            hour: now.getHours(),\n            minute: now.getMinutes()\n        };\n    };\n    const getTimeFromDate = (date)=>({\n            hour: date.getHours(),\n            minute: date.getMinutes()\n        });\n    const initializeTime = (existingTime, date)=>{\n        if (existingTime) return existingTime;\n        if (date && (date.getHours() || date.getMinutes())) return getTimeFromDate(date);\n        return shouldIncludeTime ? getCurrentTime() : null;\n    };\n    // Helper function to reset date value based on mode\n    const getEmptyDateValue = ()=>mode === \"range\" ? {\n            from: undefined,\n            to: undefined\n        } : undefined;\n    // Helper function to clear all date and time state\n    const clearAllState = ()=>{\n        setDateValue(getEmptyDateValue());\n        setPendingSelection(undefined);\n        setTime(null);\n        setToTime(null);\n        onChange(null);\n    };\n    // Helper function to render icon consistently\n    const renderIcon = function() {\n        let className = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"text-muted-foreground\";\n        if (!icon) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            size: 20,\n            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"text-neutral-400\", className)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 215,\n            columnNumber: 17\n        }, _this);\n        if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(icon)) {\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(icon, {\n                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-5 h-5\", className)\n            });\n        }\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(icon, {\n            size: 20,\n            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(className)\n        });\n    };\n    // Helper function to create Date from time object\n    const createDateFromTime = (timeObj)=>{\n        const date = new Date();\n        date.setHours(timeObj.hour, timeObj.minute, 0, 0);\n        return date;\n    };\n    // Helper function to get time picker value with confirmation logic\n    const getTimePickerValue = (currentTime, pendingDate, setTimeCallback)=>{\n        let resolvedTime = currentTime;\n        // Use time from pending selection if confirmation is enabled and meaningful\n        if (confirmSelection && pendingDate && !isNaN(pendingDate.getTime())) {\n            const pendingHours = pendingDate.getHours();\n            const pendingMinutes = pendingDate.getMinutes();\n            if (pendingHours !== 0 || pendingMinutes !== 0 || !currentTime) {\n                resolvedTime = {\n                    hour: pendingHours,\n                    minute: pendingMinutes\n                };\n            }\n        }\n        if (resolvedTime) {\n            return createDateFromTime(resolvedTime);\n        }\n        // Fallback to current time and sync state\n        const now = new Date();\n        const newTime = {\n            hour: now.getHours(),\n            minute: now.getMinutes()\n        };\n        if (setTimeCallback) setTimeCallback(newTime);\n        return now;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only update if the value has actually changed\n        if (!isValueEqual(value, dateValue)) {\n            if (value) {\n                setDateValue(value);\n                // Also update pendingSelection with the value\n                setPendingSelection(value);\n                if (value instanceof Date) {\n                    const timeFromDate = initializeTime(null, value);\n                    if (timeFromDate) setTime(timeFromDate);\n                } else if (\"from\" in value && value.from instanceof Date) {\n                    const { from, to } = value;\n                    const fromTime = initializeTime(null, from);\n                    if (fromTime) setTime(fromTime);\n                    if (to instanceof Date) {\n                        const toTimeFromDate = initializeTime(null, to);\n                        if (toTimeFromDate) setToTime(toTimeFromDate);\n                    }\n                }\n            } else {\n                // When value is null/undefined, reset to initial state but maintain format\n                setDateValue(getEmptyDateValue());\n                setPendingSelection(undefined);\n            }\n        }\n    }, [\n        value,\n        mode\n    ]);\n    const validateDate = (date)=>{\n        if (!validation) return true;\n        const { minDate, maxDate, disabledDates, disabledDaysOfWeek } = validation;\n        if (minDate && (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_10__.isBefore)(date, minDate)) return false;\n        if (maxDate && (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.isAfter)(date, maxDate)) return false;\n        if (disabledDates === null || disabledDates === void 0 ? void 0 : disabledDates.some((disabledDate)=>(0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(disabledDate, \"yyyy-MM-dd\") === (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(date, \"yyyy-MM-dd\"))) return false;\n        if (disabledDaysOfWeek === null || disabledDaysOfWeek === void 0 ? void 0 : disabledDaysOfWeek.includes(date.getDay())) return false;\n        return true;\n    };\n    const applyTime = (date, t)=>date && t ? (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.set)(date, {\n            hours: t.hour,\n            minutes: t.minute,\n            seconds: 0,\n            milliseconds: 0\n        }) : date;\n    const handleValueChange = (newValue)=>{\n        if (!newValue) {\n            // When a date is unselected, maintain the format consistency\n            setDateValue(getEmptyDateValue());\n            setPendingSelection(undefined);\n            onChange(null);\n            return;\n        }\n        if (mode === \"range\") {\n            const { from, to } = newValue;\n            if (from && !validateDate(from)) return;\n            if (to && !validateDate(to)) return;\n            // If confirmation is required, store the selection in pending state\n            if (confirmSelection) {\n                setPendingSelection({\n                    from,\n                    to\n                });\n            } else {\n                setDateValue({\n                    from,\n                    to\n                });\n                // Initialize time if needed\n                const currentTime = initializeTime(time, from);\n                if (currentTime && !time) setTime(currentTime);\n                const currentToTime = initializeTime(toTime, to);\n                if (currentToTime && !toTime) setToTime(currentToTime);\n                onChange({\n                    startDate: applyTime(from, currentTime),\n                    endDate: applyTime(to, currentToTime)\n                });\n            }\n        } else {\n            const singleDate = newValue;\n            if (!validateDate(singleDate)) return;\n            // If confirmation is required, store the selection in pending state\n            if (confirmSelection) {\n                setPendingSelection(singleDate);\n            } else {\n                setDateValue(singleDate);\n                // Initialize time preserving existing time or using date value\n                const existingDate = dateValue instanceof Date ? dateValue : undefined;\n                const currentTime = initializeTime(time, existingDate);\n                if (currentTime && !time) setTime(currentTime);\n                // Always apply time if shouldIncludeTime is true\n                const result = shouldIncludeTime ? applyTime(singleDate, currentTime) : singleDate;\n                onChange(result);\n                if (closeOnSelect && !shouldIncludeTime) setOpen(false);\n            }\n        }\n    };\n    const handleTimeChange = function(date) {\n        let isToTime = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!date) return;\n        const newTime = {\n            hour: date.getHours(),\n            minute: date.getMinutes()\n        };\n        // Check if the time has actually changed before updating state\n        if (isToTime) {\n            const currentToTime = toTime;\n            if (!currentToTime || currentToTime.hour !== newTime.hour || currentToTime.minute !== newTime.minute) {\n                setToTime(newTime);\n                // Update pendingSelection with the new time\n                if (confirmSelection && (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.to)) {\n                    const newEndDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.set)(pendingSelection.to, {\n                        hours: newTime.hour,\n                        minutes: newTime.minute,\n                        seconds: 0,\n                        milliseconds: 0\n                    });\n                    setPendingSelection({\n                        ...pendingSelection,\n                        to: newEndDate\n                    });\n                }\n                // If confirmation is not required, call onChange directly\n                if (!confirmSelection) {\n                    if (dateValue === null || dateValue === void 0 ? void 0 : dateValue.to) {\n                        const newEndDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.set)(dateValue.to, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange({\n                            startDate: dateValue.from,\n                            endDate: newEndDate\n                        });\n                    }\n                }\n            }\n        } else {\n            const currentTime = time;\n            if (!currentTime || currentTime.hour !== newTime.hour || currentTime.minute !== newTime.minute) {\n                setTime(newTime);\n                // Update pendingSelection with the new time\n                if (confirmSelection) {\n                    if (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from) {\n                        const newStartDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.set)(pendingSelection.from, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        setPendingSelection({\n                            ...pendingSelection,\n                            from: newStartDate\n                        });\n                    } else if (pendingSelection instanceof Date) {\n                        const newDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.set)(pendingSelection, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        setPendingSelection(newDate);\n                    }\n                }\n                // If confirmation is not required, call onChange directly\n                if (!confirmSelection) {\n                    if (dateValue === null || dateValue === void 0 ? void 0 : dateValue.from) {\n                        const newStartDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.set)(dateValue.from, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange({\n                            startDate: newStartDate,\n                            endDate: dateValue.to\n                        });\n                    } else if (dateValue instanceof Date) {\n                        const newDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.set)(dateValue, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange(newDate);\n                    }\n                }\n            }\n        }\n    };\n    const handleClear = (e)=>{\n        e.stopPropagation() // Prevent triggering the popover\n        ;\n        clearAllState();\n    };\n    // Function to handle clear button click inside the popover\n    const handleClearInPopover = ()=>{\n        clearAllState();\n        setOpen(false) // Close the popover after clearing\n        ;\n    };\n    // Function to handle confirmation button click\n    const handleConfirm = ()=>{\n        if (!pendingSelection) return;\n        if (mode === \"range\") {\n            const { from, to } = pendingSelection;\n            setDateValue({\n                from,\n                to\n            });\n            // Initialize time if needed\n            const currentTime = initializeTime(time, from);\n            if (currentTime && !time) setTime(currentTime);\n            const currentToTime = initializeTime(toTime, to);\n            if (currentToTime && !toTime) setToTime(currentToTime);\n            onChange({\n                startDate: applyTime(from, currentTime),\n                endDate: applyTime(to, currentToTime)\n            });\n        } else {\n            const singleDate = pendingSelection;\n            setDateValue(singleDate);\n            // Initialize time preserving existing time or using date value\n            const existingDate = dateValue instanceof Date ? dateValue : undefined;\n            const currentTime = initializeTime(time, existingDate);\n            if (currentTime && !time) setTime(currentTime);\n            const result = shouldIncludeTime ? applyTime(singleDate, currentTime) : singleDate;\n            onChange(result);\n        }\n        // Close the popover after confirmation\n        setOpen(false);\n        // Reset pending selection\n        setPendingSelection(undefined);\n    };\n    // timeOptions removed as we're now using TimePicker component\n    // Determine if we should include time based on the type prop or the deprecated includeTime prop\n    const [shouldIncludeTime, setShouldIncludeTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(type === \"datetime\" || includeTime);\n    // Update shouldIncludeTime when type or includeTime changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShouldIncludeTime(type === \"datetime\" || includeTime);\n    }, [\n        type,\n        includeTime\n    ]);\n    const displayTimeFormat = shouldIncludeTime ? \"\".concat(dateFormat, \" \").concat(timeFormat) : dateFormat;\n    // Guard against invalid dates\n    const formatDateWithTime = (date)=>{\n        if (!date) return \"\";\n        const validDate = date instanceof Date ? date : new Date();\n        return isNaN(validDate.getTime()) ? \"\" : (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(validDate, displayTimeFormat);\n    };\n    // Time picker implementation now uses the TimePicker component with mode option\n    if (disabled) {\n        // Use the provided value if available, otherwise use current date\n        const displayDate = (date)=>{\n            if (!date || !(date instanceof Date) || isNaN(date.getTime())) {\n                return (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(new Date(), dateFormat);\n            }\n            return (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(date, shouldIncludeTime ? displayTimeFormat : dateFormat);\n        };\n        // Format the date based on the mode and value\n        let displayValue;\n        if (mode === \"range\") {\n            const range = dateValue;\n            if (range === null || range === void 0 ? void 0 : range.from) {\n                displayValue = range.to ? \"\".concat(displayDate(range.from), \" - \").concat(displayDate(range.to)) : displayDate(range.from);\n            } else {\n                const currentFormatted = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(new Date(), dateFormat);\n                displayValue = \"\".concat(currentFormatted, \" - \").concat(currentFormatted);\n            }\n        } else {\n            displayValue = dateValue instanceof Date ? displayDate(dateValue) : (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(new Date(), dateFormat);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                    asChild: true,\n                    id: \"date\",\n                    position: labelPosition,\n                    disabled: disabled,\n                    children: label\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                    lineNumber: 623,\n                    columnNumber: 21\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        id: \"date\",\n                        variant: \"outline\",\n                        disabled: disabled,\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-4 justify-start w-full\"),\n                        iconLeft: renderIcon(\"mr-[8.5px] w-5 h-5 text-neutral-400\"),\n                        ...buttonProps,\n                        type: \"button\",\n                        children: displayValue\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 632,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                    lineNumber: 631,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 621,\n            columnNumber: 13\n        }, undefined);\n    }\n    const renderButtonLabel = ()=>{\n        if (mode === \"range\") {\n            const range = dateValue;\n            if (range === null || range === void 0 ? void 0 : range.from) {\n                return range.to ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        formatDateWithTime(range.from),\n                        \" -\",\n                        \" \",\n                        formatDateWithTime(range.to)\n                    ]\n                }, void 0, true) : formatDateWithTime(range.from);\n            }\n            // Use consistent date format for placeholder\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-muted-foreground\",\n                children: actualPlaceholder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 664,\n                columnNumber: 17\n            }, undefined);\n        } else if (dateValue) {\n            return formatDateWithTime(dateValue);\n        }\n        // Use consistent date format for placeholder\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-muted-foreground\",\n            children: actualPlaceholder\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 673,\n            columnNumber: 13\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(wrapperClassName),\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                id: \"date\",\n                position: labelPosition,\n                disabled: disabled,\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 680,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.Popover, {\n                modal: modal,\n                open: open,\n                onOpenChange: (isOpen)=>{\n                    // When opening the popover, initialize pendingSelection with the current value\n                    if (isOpen && !pendingSelection && dateValue) {\n                        setPendingSelection(dateValue);\n                    }\n                    setOpen(isOpen);\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.PopoverTrigger, {\n                        asChild: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    id: \"date\",\n                                    variant: \"outline\",\n                                    disabled: disabled,\n                                    iconLeft: renderIcon(),\n                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-4 justify-start w-full\", clearable && \"pr-10\"),\n                                    ...buttonProps,\n                                    type: \"button\",\n                                    children: renderButtonLabel()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 696,\n                                    columnNumber: 25\n                                }, undefined),\n                                clearable && (dateValue instanceof Date || (dateValue === null || dateValue === void 0 ? void 0 : dateValue.from)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"button\",\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    className: \"absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0\",\n                                    onClick: handleClear,\n                                    \"aria-label\": \"Clear date\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        size: 16,\n                                        className: \"text-neutral-400 hover:text-background0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 719,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 712,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 695,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 694,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.PopoverContent, {\n                        className: \"w-auto p-0\",\n                        align: \"start\",\n                        children: mode === \"range\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__.Calendar, {\n                                    autoFocus: true,\n                                    mode: \"range\",\n                                    month: (confirmSelection && pendingSelection ? pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from : dateValue === null || dateValue === void 0 ? void 0 : dateValue.from) || new Date(),\n                                    // allowFutureYears={allowFutureYears}\n                                    // showYearPicker={showYearPicker}\n                                    captionLayout: \"dropdown\",\n                                    selected: confirmSelection && pendingSelection ? pendingSelection : dateValue,\n                                    onSelect: (value)=>{\n                                        // Ensure we maintain the date format when a date is unselected\n                                        handleValueChange(value);\n                                    },\n                                    numberOfMonths: numberOfMonths,\n                                    showWeekNumber: showWeekNumbers,\n                                    disabled: validation ? (date)=>!validateDate(date) : undefined\n                                }, \"range\", false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 730,\n                                    columnNumber: 29\n                                }, undefined),\n                                shouldIncludeTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-t border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_time_picker__WEBPACK_IMPORTED_MODULE_5__.TimePicker, {\n                                            value: getTimePickerValue(time, pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from, setTime),\n                                            toValue: (()=>{\n                                                // When confirmation is enabled, use time from pendingSelection if available\n                                                let currentToTime = toTime;\n                                                if (confirmSelection && (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.to)) {\n                                                    const toDate = pendingSelection.to;\n                                                    // Only use time from pendingSelection if it has meaningful time (not 00:00)\n                                                    const pendingHours = toDate.getHours();\n                                                    const pendingMinutes = toDate.getMinutes();\n                                                    if (pendingHours !== 0 || pendingMinutes !== 0 || !toTime) {\n                                                        currentToTime = {\n                                                            hour: pendingHours,\n                                                            minute: pendingMinutes\n                                                        };\n                                                    }\n                                                // Otherwise, keep the existing toTime state\n                                                }\n                                                if (currentToTime) {\n                                                    const date = new Date();\n                                                    date.setHours(currentToTime.hour, currentToTime.minute, 0, 0);\n                                                    return date;\n                                                }\n                                                // If no time is set, use current time and sync state\n                                                const now = new Date();\n                                                const newToTime = {\n                                                    hour: now.getHours(),\n                                                    minute: now.getMinutes()\n                                                };\n                                                setToTime(newToTime);\n                                                return now;\n                                            })(),\n                                            onChange: handleTimeChange,\n                                            onToChange: (date)=>handleTimeChange(date, true),\n                                            // Always enable time picker regardless of whether a date is selected\n                                            disabled: false,\n                                            className: \"w-full\",\n                                            mode: timeMode,\n                                            label: timeMode === \"range\" ? \"Time Range\" : undefined\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 763,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 762,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 761,\n                                    columnNumber: 33\n                                }, undefined),\n                                confirmSelection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                                            className: \"my-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 838,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-3 flex gap-3 justify-end\"),\n                                            children: [\n                                                clearable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-fit\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: handleClearInPopover,\n                                                        iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                                        className: \"w-fit\",\n                                                        \"aria-label\": \"Clear date range\",\n                                                        children: \"Clear\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                        lineNumber: 845,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 844,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: handleConfirm,\n                                                    disabled: !pendingSelection,\n                                                    iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                                    children: confirmButtonText\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 857,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 839,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 729,\n                            columnNumber: 25\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__.Calendar, {\n                                    autoFocus: true,\n                                    mode: \"single\",\n                                    month: (confirmSelection && pendingSelection ? pendingSelection : dateValue) || new Date(),\n                                    selected: confirmSelection && pendingSelection ? pendingSelection : dateValue,\n                                    onSelect: (value)=>{\n                                        // Ensure we maintain the date format when a date is unselected\n                                        handleValueChange(value);\n                                    },\n                                    captionLayout: \"dropdown\",\n                                    numberOfMonths: numberOfMonths,\n                                    showWeekNumber: showWeekNumbers,\n                                    disabled: validation ? (date)=>!validateDate(date) : undefined\n                                }, \"single\", false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 869,\n                                    columnNumber: 29\n                                }, undefined),\n                                shouldIncludeTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-t border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_time_picker__WEBPACK_IMPORTED_MODULE_5__.TimePicker, {\n                                            value: (()=>{\n                                                // When confirmation is enabled, use time from pendingSelection if available\n                                                let currentTime = time;\n                                                if (confirmSelection && pendingSelection instanceof Date && !isNaN(pendingSelection.getTime())) {\n                                                    // Only use time from pendingSelection if it has meaningful time (not 00:00)\n                                                    const pendingHours = pendingSelection.getHours();\n                                                    const pendingMinutes = pendingSelection.getMinutes();\n                                                    if (pendingHours !== 0 || pendingMinutes !== 0 || !time) {\n                                                        currentTime = {\n                                                            hour: pendingHours,\n                                                            minute: pendingMinutes\n                                                        };\n                                                    }\n                                                // Otherwise, keep the existing time state\n                                                }\n                                                if (currentTime) {\n                                                    const date = new Date();\n                                                    date.setHours(currentTime.hour, currentTime.minute, 0, 0);\n                                                    return date;\n                                                }\n                                                // If no time is set, use current time and sync state\n                                                const now = new Date();\n                                                const newTime = {\n                                                    hour: now.getHours(),\n                                                    minute: now.getMinutes()\n                                                };\n                                                setTime(newTime);\n                                                return now;\n                                            })(),\n                                            toValue: (()=>{\n                                                if (toTime) {\n                                                    const date = new Date();\n                                                    date.setHours(toTime.hour, toTime.minute, 0, 0);\n                                                    return date;\n                                                }\n                                                // If no time is set, use current time and sync state\n                                                const now = new Date();\n                                                const newToTime = {\n                                                    hour: now.getHours(),\n                                                    minute: now.getMinutes()\n                                                };\n                                                setToTime(newToTime);\n                                                return now;\n                                            })(),\n                                            onChange: handleTimeChange,\n                                            onToChange: (date)=>handleTimeChange(date, true),\n                                            // Always enable time picker regardless of whether a date is selected\n                                            disabled: false,\n                                            className: \"w-full\",\n                                            mode: timeMode,\n                                            label: timeMode === \"range\" ? \"Time Range\" : undefined\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 899,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 898,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 897,\n                                    columnNumber: 33\n                                }, undefined),\n                                confirmSelection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                                            className: \"my-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 988,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-3 flex gap-3 justify-end\"),\n                                            children: [\n                                                clearable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-fit\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: handleClearInPopover,\n                                                        iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                                        \"aria-label\": \"Clear date\",\n                                                        children: \"Clear\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                        lineNumber: 995,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 994,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: handleConfirm,\n                                                    disabled: !pendingSelection,\n                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(clearable ? \"\" : \"w-full\"),\n                                                    children: confirmButtonText\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 1006,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 989,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 868,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 727,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 684,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n        lineNumber: 678,\n        columnNumber: 9\n    }, undefined);\n};\n_s(DatePicker, \"EPGvag/dmevMVfeSbcbdVrI0y2w=\");\n_c = DatePicker;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DatePicker);\nvar _c;\n$RefreshReg$(_c, \"DatePicker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DateRange.tsx\n"));

/***/ })

});