"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/DateRange.tsx":
/*!**************************************!*\
  !*** ./src/components/DateRange.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui_time_picker__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/time-picker */ \"(app-pages-browser)/./src/components/ui/time-picker.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/isBefore.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/isAfter.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/set.mjs\");\n/* harmony import */ var _ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _ui_separator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst DatePicker = (param)=>{\n    let { onChange, className, placeholder = \"Select date\", mode = \"range\", type = \"date\", disabled = false, value, dateFormat = \"dd LLLL, y\", validation, numberOfMonths = mode === \"range\" ? 2 : 1, closeOnSelect = true, showWeekNumbers = false, includeTime = false, timeMode = \"single\", timeFormat = \"HH:mm\", timeInterval = 30, label, labelPosition = \"top\", clearable = false, icon, confirmSelection = true, confirmButtonText = \"Confirm\", modal = false, wrapperClassName = \"\", ...buttonProps } = param;\n    _s();\n    const [dateValue, setDateValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value || (mode === \"range\" ? {\n        from: undefined,\n        to: undefined\n    } : undefined));\n    // We'll use buttonProps directly but ensure we don't pass our custom type prop to the Button component\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [time, setTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [toTime, setToTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State to track pending selection when confirmation button is enabled\n    const [pendingSelection, setPendingSelection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value || undefined);\n    // Set placeholder based on mode\n    const actualPlaceholder = mode === \"range\" ? \"Select date range\" : \"Select date\";\n    // Optimized deep equality check for dates\n    const isDateEqual = (a, b)=>{\n        if (a === b) return true;\n        if (!a || !b) return false;\n        return a instanceof Date && b instanceof Date && a.getTime() === b.getTime();\n    };\n    const isValueEqual = (a, b)=>{\n        if (a === b) return true;\n        if (a instanceof Date && b instanceof Date) return isDateEqual(a, b);\n        if (a && b && typeof a === \"object\" && typeof b === \"object\" && \"from\" in a && \"to\" in a && \"from\" in b && \"to\" in b) {\n            return isDateEqual(a.from, b.from) && isDateEqual(a.to, b.to);\n        }\n        return false;\n    };\n    // Helper functions for time initialization\n    const getCurrentTime = ()=>{\n        const now = new Date();\n        return {\n            hour: now.getHours(),\n            minute: now.getMinutes()\n        };\n    };\n    const getTimeFromDate = (date)=>({\n            hour: date.getHours(),\n            minute: date.getMinutes()\n        });\n    const initializeTime = (existingTime, date)=>{\n        if (existingTime) return existingTime;\n        if (date && (date.getHours() || date.getMinutes())) return getTimeFromDate(date);\n        return shouldIncludeTime ? getCurrentTime() : null;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only update if the value has actually changed\n        if (!isValueEqual(value, dateValue)) {\n            if (value) {\n                setDateValue(value);\n                // Also update pendingSelection with the value\n                setPendingSelection(value);\n                if (value instanceof Date) {\n                    const timeFromDate = initializeTime(null, value);\n                    if (timeFromDate) setTime(timeFromDate);\n                } else if (\"from\" in value && value.from instanceof Date) {\n                    const { from, to } = value;\n                    const fromTime = initializeTime(null, from);\n                    if (fromTime) setTime(fromTime);\n                    if (to instanceof Date) {\n                        const toTimeFromDate = initializeTime(null, to);\n                        if (toTimeFromDate) setToTime(toTimeFromDate);\n                    }\n                }\n            } else {\n                // When value is null/undefined, reset to initial state but maintain format\n                setDateValue(mode === \"range\" ? {\n                    from: undefined,\n                    to: undefined\n                } : undefined);\n                // Also reset pendingSelection\n                setPendingSelection(undefined);\n            }\n        }\n    }, [\n        value,\n        mode\n    ]);\n    const validateDate = (date)=>{\n        if (!validation) return true;\n        const { minDate, maxDate, disabledDates, disabledDaysOfWeek } = validation;\n        if (minDate && (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_9__.isBefore)(date, minDate)) return false;\n        if (maxDate && (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_10__.isAfter)(date, maxDate)) return false;\n        if (disabledDates === null || disabledDates === void 0 ? void 0 : disabledDates.some((disabledDate)=>(0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(disabledDate, \"yyyy-MM-dd\") === (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(date, \"yyyy-MM-dd\"))) return false;\n        if (disabledDaysOfWeek === null || disabledDaysOfWeek === void 0 ? void 0 : disabledDaysOfWeek.includes(date.getDay())) return false;\n        return true;\n    };\n    const applyTime = (date, t)=>date && t ? (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(date, {\n            hours: t.hour,\n            minutes: t.minute,\n            seconds: 0,\n            milliseconds: 0\n        }) : date;\n    const handleValueChange = (newValue)=>{\n        if (!newValue) {\n            // When a date is unselected, maintain the format consistency\n            setDateValue(mode === \"range\" ? {\n                from: undefined,\n                to: undefined\n            } : undefined);\n            setPendingSelection(undefined);\n            onChange(null);\n            return;\n        }\n        if (mode === \"range\") {\n            const { from, to } = newValue;\n            if (from && !validateDate(from)) return;\n            if (to && !validateDate(to)) return;\n            // If confirmation is required, store the selection in pending state\n            if (confirmSelection) {\n                setPendingSelection({\n                    from,\n                    to\n                });\n            } else {\n                setDateValue({\n                    from,\n                    to\n                });\n                // Initialize time if needed\n                const currentTime = initializeTime(time, from);\n                if (currentTime && !time) setTime(currentTime);\n                const currentToTime = initializeTime(toTime, to);\n                if (currentToTime && !toTime) setToTime(currentToTime);\n                onChange({\n                    startDate: applyTime(from, currentTime),\n                    endDate: applyTime(to, currentToTime)\n                });\n            }\n        } else {\n            const singleDate = newValue;\n            if (!validateDate(singleDate)) return;\n            // If confirmation is required, store the selection in pending state\n            if (confirmSelection) {\n                setPendingSelection(singleDate);\n            } else {\n                setDateValue(singleDate);\n                // Initialize time preserving existing time or using date value\n                const existingDate = dateValue instanceof Date ? dateValue : undefined;\n                const currentTime = initializeTime(time, existingDate);\n                if (currentTime && !time) setTime(currentTime);\n                // Always apply time if shouldIncludeTime is true\n                const result = shouldIncludeTime ? applyTime(singleDate, currentTime) : singleDate;\n                onChange(result);\n                if (closeOnSelect && !shouldIncludeTime) setOpen(false);\n            }\n        }\n    };\n    const handleTimeChange = function(date) {\n        let isToTime = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!date) return;\n        const newTime = {\n            hour: date.getHours(),\n            minute: date.getMinutes()\n        };\n        // Check if the time has actually changed before updating state\n        if (isToTime) {\n            const currentToTime = toTime;\n            if (!currentToTime || currentToTime.hour !== newTime.hour || currentToTime.minute !== newTime.minute) {\n                setToTime(newTime);\n                // Update pendingSelection with the new time\n                if (confirmSelection && (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.to)) {\n                    const newEndDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(pendingSelection.to, {\n                        hours: newTime.hour,\n                        minutes: newTime.minute,\n                        seconds: 0,\n                        milliseconds: 0\n                    });\n                    setPendingSelection({\n                        ...pendingSelection,\n                        to: newEndDate\n                    });\n                }\n                // If confirmation is not required, call onChange directly\n                if (!confirmSelection) {\n                    if (dateValue === null || dateValue === void 0 ? void 0 : dateValue.to) {\n                        const newEndDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(dateValue.to, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange({\n                            startDate: dateValue.from,\n                            endDate: newEndDate\n                        });\n                    }\n                }\n            }\n        } else {\n            const currentTime = time;\n            if (!currentTime || currentTime.hour !== newTime.hour || currentTime.minute !== newTime.minute) {\n                setTime(newTime);\n                // Update pendingSelection with the new time\n                if (confirmSelection) {\n                    if (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from) {\n                        const newStartDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(pendingSelection.from, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        setPendingSelection({\n                            ...pendingSelection,\n                            from: newStartDate\n                        });\n                    } else if (pendingSelection instanceof Date) {\n                        const newDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(pendingSelection, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        setPendingSelection(newDate);\n                    }\n                }\n                // If confirmation is not required, call onChange directly\n                if (!confirmSelection) {\n                    if (dateValue === null || dateValue === void 0 ? void 0 : dateValue.from) {\n                        const newStartDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(dateValue.from, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange({\n                            startDate: newStartDate,\n                            endDate: dateValue.to\n                        });\n                    } else if (dateValue instanceof Date) {\n                        const newDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.set)(dateValue, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange(newDate);\n                    }\n                }\n            }\n        }\n    };\n    const handleClear = (e)=>{\n        e.stopPropagation() // Prevent triggering the popover\n        ;\n        setDateValue(mode === \"range\" ? {\n            from: undefined,\n            to: undefined\n        } : undefined);\n        setPendingSelection(undefined);\n        setTime(null);\n        setToTime(null);\n        onChange(null);\n    };\n    // Function to handle clear button click inside the popover\n    const handleClearInPopover = ()=>{\n        setDateValue(mode === \"range\" ? {\n            from: undefined,\n            to: undefined\n        } : undefined);\n        setPendingSelection(undefined);\n        setTime(null);\n        setToTime(null);\n        onChange(null);\n        // Close the popover after clearing\n        setOpen(false);\n    };\n    // Function to handle confirmation button click\n    const handleConfirm = ()=>{\n        if (!pendingSelection) return;\n        if (mode === \"range\") {\n            const { from, to } = pendingSelection;\n            setDateValue({\n                from,\n                to\n            });\n            // Initialize time if needed\n            const currentTime = initializeTime(time, from);\n            if (currentTime && !time) setTime(currentTime);\n            const currentToTime = initializeTime(toTime, to);\n            if (currentToTime && !toTime) setToTime(currentToTime);\n            onChange({\n                startDate: applyTime(from, currentTime),\n                endDate: applyTime(to, currentToTime)\n            });\n        } else {\n            const singleDate = pendingSelection;\n            setDateValue(singleDate);\n            // Apply time if needed\n            let currentTime = time;\n            // If we have an existing dateValue with time, preserve that time\n            if (!currentTime && shouldIncludeTime && dateValue instanceof Date) {\n                currentTime = {\n                    hour: dateValue.getHours(),\n                    minute: dateValue.getMinutes()\n                };\n                setTime(currentTime);\n            } else if (!currentTime && shouldIncludeTime) {\n                // Only use current time if no existing time is available\n                const now = new Date();\n                currentTime = {\n                    hour: now.getHours(),\n                    minute: now.getMinutes()\n                };\n                setTime(currentTime);\n            }\n            const result = shouldIncludeTime ? applyTime(singleDate, currentTime) : singleDate;\n            onChange(result);\n        }\n        // Close the popover after confirmation\n        setOpen(false);\n        // Reset pending selection\n        setPendingSelection(undefined);\n    };\n    // timeOptions removed as we're now using TimePicker component\n    // Determine if we should include time based on the type prop or the deprecated includeTime prop\n    const [shouldIncludeTime, setShouldIncludeTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(type === \"datetime\" || includeTime);\n    // Update shouldIncludeTime when type or includeTime changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShouldIncludeTime(type === \"datetime\" || includeTime);\n    }, [\n        type,\n        includeTime\n    ]);\n    const displayTimeFormat = shouldIncludeTime ? \"\".concat(dateFormat, \" \").concat(timeFormat) : dateFormat;\n    // Guard against invalid dates\n    const formatDateWithTime = (date)=>{\n        if (!date) return \"\";\n        const validDate = date instanceof Date ? date : new Date();\n        return isNaN(validDate.getTime()) ? \"\" : (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(validDate, displayTimeFormat);\n    };\n    // Time picker implementation now uses the TimePicker component with mode option\n    if (disabled) {\n        // Use the provided value if available, otherwise use current date\n        const displayDate = (date)=>{\n            if (!date || !(date instanceof Date) || isNaN(date.getTime())) {\n                return (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(new Date(), dateFormat);\n            }\n            return (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(date, shouldIncludeTime ? displayTimeFormat : dateFormat);\n        };\n        // Format the date based on the mode and value\n        let displayValue;\n        if (mode === \"range\") {\n            const range = dateValue;\n            if (range === null || range === void 0 ? void 0 : range.from) {\n                displayValue = range.to ? \"\".concat(displayDate(range.from), \" - \").concat(displayDate(range.to)) : displayDate(range.from);\n            } else {\n                const currentFormatted = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(new Date(), dateFormat);\n                displayValue = \"\".concat(currentFormatted, \" - \").concat(currentFormatted);\n            }\n        } else {\n            displayValue = dateValue instanceof Date ? displayDate(dateValue) : (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(new Date(), dateFormat);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                    asChild: true,\n                    id: \"date\",\n                    position: labelPosition,\n                    disabled: disabled,\n                    children: label\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                    lineNumber: 590,\n                    columnNumber: 21\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        id: \"date\",\n                        variant: \"outline\",\n                        disabled: disabled,\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-4 justify-start w-full\"),\n                        iconLeft: icon ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(icon) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(icon, {\n                            className: \"mr-[8.5px] w-5 h-5 text-neutral-400\"\n                        }) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(icon, {\n                            size: 20,\n                            className: \"mr-[8.5px] w-5 h-5 text-neutral-400\"\n                        }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"mr-[8.5px] w-5 h-5 text-neutral-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 622,\n                            columnNumber: 33\n                        }, void 0),\n                        ...buttonProps,\n                        type: \"button\",\n                        children: displayValue\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 599,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                    lineNumber: 598,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 588,\n            columnNumber: 13\n        }, undefined);\n    }\n    const renderButtonLabel = ()=>{\n        if (mode === \"range\") {\n            const range = dateValue;\n            if (range === null || range === void 0 ? void 0 : range.from) {\n                return range.to ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        formatDateWithTime(range.from),\n                        \" -\",\n                        \" \",\n                        formatDateWithTime(range.to)\n                    ]\n                }, void 0, true) : formatDateWithTime(range.from);\n            }\n            // Use consistent date format for placeholder\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-muted-foreground\",\n                children: actualPlaceholder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 649,\n                columnNumber: 17\n            }, undefined);\n        } else if (dateValue) {\n            return formatDateWithTime(dateValue);\n        }\n        // Use consistent date format for placeholder\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-muted-foreground\",\n            children: actualPlaceholder\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 658,\n            columnNumber: 13\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(wrapperClassName),\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                id: \"date\",\n                position: labelPosition,\n                disabled: disabled,\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 665,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.Popover, {\n                modal: modal,\n                open: open,\n                onOpenChange: (isOpen)=>{\n                    // When opening the popover, initialize pendingSelection with the current value\n                    if (isOpen && !pendingSelection && dateValue) {\n                        setPendingSelection(dateValue);\n                    }\n                    setOpen(isOpen);\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.PopoverTrigger, {\n                        asChild: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    id: \"date\",\n                                    variant: \"outline\",\n                                    disabled: disabled,\n                                    iconLeft: icon ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(icon) ? icon : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(icon, {\n                                        size: 20,\n                                        className: \"text-muted-foreground\"\n                                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        size: 20,\n                                        className: \"text-neutral-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 696,\n                                        columnNumber: 37\n                                    }, void 0),\n                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-4 justify-start w-full\", clearable && \"pr-10\"),\n                                    ...buttonProps,\n                                    type: \"button\",\n                                    children: renderButtonLabel()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 681,\n                                    columnNumber: 25\n                                }, undefined),\n                                clearable && (dateValue instanceof Date || (dateValue === null || dateValue === void 0 ? void 0 : dateValue.from)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"button\",\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    className: \"absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0\",\n                                    onClick: handleClear,\n                                    \"aria-label\": \"Clear date\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        size: 16,\n                                        className: \"text-neutral-400 hover:text-background0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 720,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 713,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 680,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 679,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.PopoverContent, {\n                        className: \"w-auto p-0\",\n                        align: \"start\",\n                        children: mode === \"range\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__.Calendar, {\n                                    autoFocus: true,\n                                    mode: \"range\",\n                                    month: (confirmSelection && pendingSelection ? pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from : dateValue === null || dateValue === void 0 ? void 0 : dateValue.from) || new Date(),\n                                    // allowFutureYears={allowFutureYears}\n                                    // showYearPicker={showYearPicker}\n                                    captionLayout: \"dropdown\",\n                                    selected: confirmSelection && pendingSelection ? pendingSelection : dateValue,\n                                    onSelect: (value)=>{\n                                        // Ensure we maintain the date format when a date is unselected\n                                        handleValueChange(value);\n                                    },\n                                    numberOfMonths: numberOfMonths,\n                                    showWeekNumber: showWeekNumbers,\n                                    disabled: validation ? (date)=>!validateDate(date) : undefined\n                                }, \"range\", false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 731,\n                                    columnNumber: 29\n                                }, undefined),\n                                shouldIncludeTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-t border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_time_picker__WEBPACK_IMPORTED_MODULE_5__.TimePicker, {\n                                            value: (()=>{\n                                                // When confirmation is enabled, use time from pendingSelection if available\n                                                let currentTime = time;\n                                                if (confirmSelection && (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from)) {\n                                                    const fromDate = pendingSelection.from;\n                                                    // Only use time from pendingSelection if it has meaningful time (not 00:00)\n                                                    const pendingHours = fromDate.getHours();\n                                                    const pendingMinutes = fromDate.getMinutes();\n                                                    if (pendingHours !== 0 || pendingMinutes !== 0 || !time) {\n                                                        currentTime = {\n                                                            hour: pendingHours,\n                                                            minute: pendingMinutes\n                                                        };\n                                                    }\n                                                // Otherwise, keep the existing time state\n                                                }\n                                                if (currentTime) {\n                                                    const date = new Date();\n                                                    date.setHours(currentTime.hour, currentTime.minute, 0, 0);\n                                                    return date;\n                                                }\n                                                // If no time is set, use current time and sync state\n                                                const now = new Date();\n                                                const newTime = {\n                                                    hour: now.getHours(),\n                                                    minute: now.getMinutes()\n                                                };\n                                                setTime(newTime);\n                                                return now;\n                                            })(),\n                                            toValue: (()=>{\n                                                // When confirmation is enabled, use time from pendingSelection if available\n                                                let currentToTime = toTime;\n                                                if (confirmSelection && (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.to)) {\n                                                    const toDate = pendingSelection.to;\n                                                    // Only use time from pendingSelection if it has meaningful time (not 00:00)\n                                                    const pendingHours = toDate.getHours();\n                                                    const pendingMinutes = toDate.getMinutes();\n                                                    if (pendingHours !== 0 || pendingMinutes !== 0 || !toTime) {\n                                                        currentToTime = {\n                                                            hour: pendingHours,\n                                                            minute: pendingMinutes\n                                                        };\n                                                    }\n                                                // Otherwise, keep the existing toTime state\n                                                }\n                                                if (currentToTime) {\n                                                    const date = new Date();\n                                                    date.setHours(currentToTime.hour, currentToTime.minute, 0, 0);\n                                                    return date;\n                                                }\n                                                // If no time is set, use current time and sync state\n                                                const now = new Date();\n                                                const newToTime = {\n                                                    hour: now.getHours(),\n                                                    minute: now.getMinutes()\n                                                };\n                                                setToTime(newToTime);\n                                                return now;\n                                            })(),\n                                            onChange: handleTimeChange,\n                                            onToChange: (date)=>handleTimeChange(date, true),\n                                            // Always enable time picker regardless of whether a date is selected\n                                            disabled: false,\n                                            className: \"w-full\",\n                                            mode: timeMode,\n                                            label: timeMode === \"range\" ? \"Time Range\" : undefined\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 764,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 763,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 762,\n                                    columnNumber: 33\n                                }, undefined),\n                                confirmSelection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                                            className: \"my-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 882,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-3 flex gap-3 justify-end\"),\n                                            children: [\n                                                clearable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-fit\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: handleClearInPopover,\n                                                        iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                                        className: \"w-fit\",\n                                                        \"aria-label\": \"Clear date range\",\n                                                        children: \"Clear\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                        lineNumber: 889,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 888,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: handleConfirm,\n                                                    disabled: !pendingSelection,\n                                                    iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                                    children: confirmButtonText\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 901,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 883,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 730,\n                            columnNumber: 25\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__.Calendar, {\n                                    autoFocus: true,\n                                    mode: \"single\",\n                                    month: (confirmSelection && pendingSelection ? pendingSelection : dateValue) || new Date(),\n                                    selected: confirmSelection && pendingSelection ? pendingSelection : dateValue,\n                                    onSelect: (value)=>{\n                                        // Ensure we maintain the date format when a date is unselected\n                                        handleValueChange(value);\n                                    },\n                                    captionLayout: \"dropdown\",\n                                    numberOfMonths: numberOfMonths,\n                                    showWeekNumber: showWeekNumbers,\n                                    disabled: validation ? (date)=>!validateDate(date) : undefined\n                                }, \"single\", false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 913,\n                                    columnNumber: 29\n                                }, undefined),\n                                shouldIncludeTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-t border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_time_picker__WEBPACK_IMPORTED_MODULE_5__.TimePicker, {\n                                            value: (()=>{\n                                                // When confirmation is enabled, use time from pendingSelection if available\n                                                let currentTime = time;\n                                                if (confirmSelection && pendingSelection instanceof Date && !isNaN(pendingSelection.getTime())) {\n                                                    // Only use time from pendingSelection if it has meaningful time (not 00:00)\n                                                    const pendingHours = pendingSelection.getHours();\n                                                    const pendingMinutes = pendingSelection.getMinutes();\n                                                    if (pendingHours !== 0 || pendingMinutes !== 0 || !time) {\n                                                        currentTime = {\n                                                            hour: pendingHours,\n                                                            minute: pendingMinutes\n                                                        };\n                                                    }\n                                                // Otherwise, keep the existing time state\n                                                }\n                                                if (currentTime) {\n                                                    const date = new Date();\n                                                    date.setHours(currentTime.hour, currentTime.minute, 0, 0);\n                                                    return date;\n                                                }\n                                                // If no time is set, use current time and sync state\n                                                const now = new Date();\n                                                const newTime = {\n                                                    hour: now.getHours(),\n                                                    minute: now.getMinutes()\n                                                };\n                                                setTime(newTime);\n                                                return now;\n                                            })(),\n                                            toValue: (()=>{\n                                                if (toTime) {\n                                                    const date = new Date();\n                                                    date.setHours(toTime.hour, toTime.minute, 0, 0);\n                                                    return date;\n                                                }\n                                                // If no time is set, use current time and sync state\n                                                const now = new Date();\n                                                const newToTime = {\n                                                    hour: now.getHours(),\n                                                    minute: now.getMinutes()\n                                                };\n                                                setToTime(newToTime);\n                                                return now;\n                                            })(),\n                                            onChange: handleTimeChange,\n                                            onToChange: (date)=>handleTimeChange(date, true),\n                                            // Always enable time picker regardless of whether a date is selected\n                                            disabled: false,\n                                            className: \"w-full\",\n                                            mode: timeMode,\n                                            label: timeMode === \"range\" ? \"Time Range\" : undefined\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 943,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 942,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 941,\n                                    columnNumber: 33\n                                }, undefined),\n                                confirmSelection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                                            className: \"my-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 1032,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-3 flex gap-3 justify-end\"),\n                                            children: [\n                                                clearable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-fit\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: handleClearInPopover,\n                                                        iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                                        \"aria-label\": \"Clear date\",\n                                                        children: \"Clear\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                        lineNumber: 1039,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 1038,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: handleConfirm,\n                                                    disabled: !pendingSelection,\n                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(clearable ? \"\" : \"w-full\"),\n                                                    children: confirmButtonText\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 1050,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 1033,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 912,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 728,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 669,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n        lineNumber: 663,\n        columnNumber: 9\n    }, undefined);\n};\n_s(DatePicker, \"EPGvag/dmevMVfeSbcbdVrI0y2w=\");\n_c = DatePicker;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DatePicker);\nvar _c;\n$RefreshReg$(_c, \"DatePicker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DateRange.tsx\n"));

/***/ })

});