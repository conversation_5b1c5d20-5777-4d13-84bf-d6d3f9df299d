"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/DateRange.tsx":
/*!**************************************!*\
  !*** ./src/components/DateRange.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui_time_picker__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/time-picker */ \"(app-pages-browser)/./src/components/ui/time-picker.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/isBefore.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/isAfter.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/set.mjs\");\n/* harmony import */ var _ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _ui_separator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst DatePicker = (param)=>{\n    let { onChange, className, placeholder = \"Select date\", mode = \"range\", type = \"date\", disabled = false, value, dateFormat = \"dd LLLL, y\", validation, numberOfMonths = mode === \"range\" ? 2 : 1, closeOnSelect = true, showWeekNumbers = false, includeTime = false, timeMode = \"single\", timeFormat = \"HH:mm\", timeInterval = 30, label, labelPosition = \"top\", clearable = false, icon, confirmSelection = true, confirmButtonText = \"Confirm\", modal = false, wrapperClassName = \"\", ...buttonProps } = param;\n    var _this = undefined;\n    _s();\n    const [dateValue, setDateValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value || (mode === \"range\" ? {\n        from: undefined,\n        to: undefined\n    } : undefined));\n    // We'll use buttonProps directly but ensure we don't pass our custom type prop to the Button component\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [time, setTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [toTime, setToTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State to track pending selection when confirmation button is enabled\n    const [pendingSelection, setPendingSelection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value || undefined);\n    // Set placeholder based on mode\n    const actualPlaceholder = mode === \"range\" ? \"Select date range\" : \"Select date\";\n    // Optimized deep equality check for dates\n    const isDateEqual = (a, b)=>{\n        if (a === b) return true;\n        if (!a || !b) return false;\n        return a instanceof Date && b instanceof Date && a.getTime() === b.getTime();\n    };\n    const isValueEqual = (a, b)=>{\n        if (a === b) return true;\n        if (a instanceof Date && b instanceof Date) return isDateEqual(a, b);\n        if (a && b && typeof a === \"object\" && typeof b === \"object\" && \"from\" in a && \"to\" in a && \"from\" in b && \"to\" in b) {\n            return isDateEqual(a.from, b.from) && isDateEqual(a.to, b.to);\n        }\n        return false;\n    };\n    // Helper functions for time initialization\n    const getCurrentTime = ()=>{\n        const now = new Date();\n        return {\n            hour: now.getHours(),\n            minute: now.getMinutes()\n        };\n    };\n    const getTimeFromDate = (date)=>({\n            hour: date.getHours(),\n            minute: date.getMinutes()\n        });\n    const initializeTime = (existingTime, date)=>{\n        if (existingTime) return existingTime;\n        if (date && (date.getHours() || date.getMinutes())) return getTimeFromDate(date);\n        return shouldIncludeTime ? getCurrentTime() : null;\n    };\n    // Helper function to reset date value based on mode\n    const getEmptyDateValue = ()=>mode === \"range\" ? {\n            from: undefined,\n            to: undefined\n        } : undefined;\n    // Helper function to clear all date and time state\n    const clearAllState = ()=>{\n        setDateValue(getEmptyDateValue());\n        setPendingSelection(undefined);\n        setTime(null);\n        setToTime(null);\n        onChange(null);\n    };\n    // Helper function to render icon consistently\n    const renderIcon = function() {\n        let className = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"text-muted-foreground\";\n        if (!icon) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            size: 20,\n            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"text-neutral-400\", className)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 215,\n            columnNumber: 17\n        }, _this);\n        if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(icon)) {\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(icon, {\n                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-5 h-5\", className)\n            });\n        }\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(icon, {\n            size: 20,\n            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(className)\n        });\n    };\n    // Helper function to create Date from time object\n    const createDateFromTime = (timeObj)=>{\n        const date = new Date();\n        date.setHours(timeObj.hour, timeObj.minute, 0, 0);\n        return date;\n    };\n    // Helper function to get time picker value with confirmation logic\n    const getTimePickerValue = (currentTime, pendingDate, setTimeCallback)=>{\n        let resolvedTime = currentTime;\n        // Use time from pending selection if confirmation is enabled and meaningful\n        if (confirmSelection && pendingDate && !isNaN(pendingDate.getTime())) {\n            const pendingHours = pendingDate.getHours();\n            const pendingMinutes = pendingDate.getMinutes();\n            if (pendingHours !== 0 || pendingMinutes !== 0 || !currentTime) {\n                resolvedTime = {\n                    hour: pendingHours,\n                    minute: pendingMinutes\n                };\n            }\n        }\n        if (resolvedTime) {\n            return createDateFromTime(resolvedTime);\n        }\n        // Fallback to current time and sync state\n        const now = new Date();\n        const newTime = {\n            hour: now.getHours(),\n            minute: now.getMinutes()\n        };\n        if (setTimeCallback) setTimeCallback(newTime);\n        return now;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only update if the value has actually changed\n        if (!isValueEqual(value, dateValue)) {\n            if (value) {\n                setDateValue(value);\n                // Also update pendingSelection with the value\n                setPendingSelection(value);\n                if (value instanceof Date) {\n                    const timeFromDate = initializeTime(null, value);\n                    if (timeFromDate) setTime(timeFromDate);\n                } else if (\"from\" in value && value.from instanceof Date) {\n                    const { from, to } = value;\n                    const fromTime = initializeTime(null, from);\n                    if (fromTime) setTime(fromTime);\n                    if (to instanceof Date) {\n                        const toTimeFromDate = initializeTime(null, to);\n                        if (toTimeFromDate) setToTime(toTimeFromDate);\n                    }\n                }\n            } else {\n                // When value is null/undefined, reset to initial state but maintain format\n                setDateValue(getEmptyDateValue());\n                setPendingSelection(undefined);\n            }\n        }\n    }, [\n        value,\n        mode\n    ]);\n    const validateDate = (date)=>{\n        if (!validation) return true;\n        const { minDate, maxDate, disabledDates, disabledDaysOfWeek } = validation;\n        if (minDate && (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_10__.isBefore)(date, minDate)) return false;\n        if (maxDate && (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.isAfter)(date, maxDate)) return false;\n        if (disabledDates === null || disabledDates === void 0 ? void 0 : disabledDates.some((disabledDate)=>(0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(disabledDate, \"yyyy-MM-dd\") === (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(date, \"yyyy-MM-dd\"))) return false;\n        if (disabledDaysOfWeek === null || disabledDaysOfWeek === void 0 ? void 0 : disabledDaysOfWeek.includes(date.getDay())) return false;\n        return true;\n    };\n    const applyTime = (date, t)=>date && t ? (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.set)(date, {\n            hours: t.hour,\n            minutes: t.minute,\n            seconds: 0,\n            milliseconds: 0\n        }) : date;\n    const handleValueChange = (newValue)=>{\n        if (!newValue) {\n            // When a date is unselected, maintain the format consistency\n            setDateValue(getEmptyDateValue());\n            setPendingSelection(undefined);\n            onChange(null);\n            return;\n        }\n        if (mode === \"range\") {\n            const { from, to } = newValue;\n            if (from && !validateDate(from)) return;\n            if (to && !validateDate(to)) return;\n            // If confirmation is required, store the selection in pending state\n            if (confirmSelection) {\n                setPendingSelection({\n                    from,\n                    to\n                });\n            } else {\n                setDateValue({\n                    from,\n                    to\n                });\n                // Initialize time if needed\n                const currentTime = initializeTime(time, from);\n                if (currentTime && !time) setTime(currentTime);\n                const currentToTime = initializeTime(toTime, to);\n                if (currentToTime && !toTime) setToTime(currentToTime);\n                onChange({\n                    startDate: applyTime(from, currentTime),\n                    endDate: applyTime(to, currentToTime)\n                });\n            }\n        } else {\n            const singleDate = newValue;\n            if (!validateDate(singleDate)) return;\n            // If confirmation is required, store the selection in pending state\n            if (confirmSelection) {\n                setPendingSelection(singleDate);\n            } else {\n                setDateValue(singleDate);\n                // Initialize time preserving existing time or using date value\n                const existingDate = dateValue instanceof Date ? dateValue : undefined;\n                const currentTime = initializeTime(time, existingDate);\n                if (currentTime && !time) setTime(currentTime);\n                // Always apply time if shouldIncludeTime is true\n                const result = shouldIncludeTime ? applyTime(singleDate, currentTime) : singleDate;\n                onChange(result);\n                if (closeOnSelect && !shouldIncludeTime) setOpen(false);\n            }\n        }\n    };\n    const handleTimeChange = function(date) {\n        let isToTime = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!date) return;\n        const newTime = {\n            hour: date.getHours(),\n            minute: date.getMinutes()\n        };\n        // Check if the time has actually changed before updating state\n        if (isToTime) {\n            const currentToTime = toTime;\n            if (!currentToTime || currentToTime.hour !== newTime.hour || currentToTime.minute !== newTime.minute) {\n                setToTime(newTime);\n                // Update pendingSelection with the new time\n                if (confirmSelection && (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.to)) {\n                    const newEndDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.set)(pendingSelection.to, {\n                        hours: newTime.hour,\n                        minutes: newTime.minute,\n                        seconds: 0,\n                        milliseconds: 0\n                    });\n                    setPendingSelection({\n                        ...pendingSelection,\n                        to: newEndDate\n                    });\n                }\n                // If confirmation is not required, call onChange directly\n                if (!confirmSelection) {\n                    if (dateValue === null || dateValue === void 0 ? void 0 : dateValue.to) {\n                        const newEndDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.set)(dateValue.to, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange({\n                            startDate: dateValue.from,\n                            endDate: newEndDate\n                        });\n                    }\n                }\n            }\n        } else {\n            const currentTime = time;\n            if (!currentTime || currentTime.hour !== newTime.hour || currentTime.minute !== newTime.minute) {\n                setTime(newTime);\n                // Update pendingSelection with the new time\n                if (confirmSelection) {\n                    if (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from) {\n                        const newStartDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.set)(pendingSelection.from, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        setPendingSelection({\n                            ...pendingSelection,\n                            from: newStartDate\n                        });\n                    } else if (pendingSelection instanceof Date) {\n                        const newDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.set)(pendingSelection, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        setPendingSelection(newDate);\n                    }\n                }\n                // If confirmation is not required, call onChange directly\n                if (!confirmSelection) {\n                    if (dateValue === null || dateValue === void 0 ? void 0 : dateValue.from) {\n                        const newStartDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.set)(dateValue.from, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange({\n                            startDate: newStartDate,\n                            endDate: dateValue.to\n                        });\n                    } else if (dateValue instanceof Date) {\n                        const newDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.set)(dateValue, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange(newDate);\n                    }\n                }\n            }\n        }\n    };\n    const handleClear = (e)=>{\n        e.stopPropagation() // Prevent triggering the popover\n        ;\n        clearAllState();\n    };\n    // Function to handle clear button click inside the popover\n    const handleClearInPopover = ()=>{\n        clearAllState();\n        setOpen(false) // Close the popover after clearing\n        ;\n    };\n    // Function to handle confirmation button click\n    const handleConfirm = ()=>{\n        if (!pendingSelection) return;\n        if (mode === \"range\") {\n            const { from, to } = pendingSelection;\n            setDateValue({\n                from,\n                to\n            });\n            // Initialize time if needed\n            const currentTime = initializeTime(time, from);\n            if (currentTime && !time) setTime(currentTime);\n            const currentToTime = initializeTime(toTime, to);\n            if (currentToTime && !toTime) setToTime(currentToTime);\n            onChange({\n                startDate: applyTime(from, currentTime),\n                endDate: applyTime(to, currentToTime)\n            });\n        } else {\n            const singleDate = pendingSelection;\n            setDateValue(singleDate);\n            // Initialize time preserving existing time or using date value\n            const existingDate = dateValue instanceof Date ? dateValue : undefined;\n            const currentTime = initializeTime(time, existingDate);\n            if (currentTime && !time) setTime(currentTime);\n            const result = shouldIncludeTime ? applyTime(singleDate, currentTime) : singleDate;\n            onChange(result);\n        }\n        // Close the popover after confirmation\n        setOpen(false);\n        // Reset pending selection\n        setPendingSelection(undefined);\n    };\n    // timeOptions removed as we're now using TimePicker component\n    // Determine if we should include time based on the type prop or the deprecated includeTime prop\n    const [shouldIncludeTime, setShouldIncludeTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(type === \"datetime\" || includeTime);\n    // Update shouldIncludeTime when type or includeTime changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShouldIncludeTime(type === \"datetime\" || includeTime);\n    }, [\n        type,\n        includeTime\n    ]);\n    const displayTimeFormat = shouldIncludeTime ? \"\".concat(dateFormat, \" \").concat(timeFormat) : dateFormat;\n    // Guard against invalid dates\n    const formatDateWithTime = (date)=>{\n        if (!date) return \"\";\n        const validDate = date instanceof Date ? date : new Date();\n        return isNaN(validDate.getTime()) ? \"\" : (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(validDate, displayTimeFormat);\n    };\n    // Time picker implementation now uses the TimePicker component with mode option\n    if (disabled) {\n        // Use the provided value if available, otherwise use current date\n        const displayDate = (date)=>{\n            if (!date || !(date instanceof Date) || isNaN(date.getTime())) {\n                return (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(new Date(), dateFormat);\n            }\n            return (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(date, shouldIncludeTime ? displayTimeFormat : dateFormat);\n        };\n        // Format the date based on the mode and value\n        let displayValue;\n        if (mode === \"range\") {\n            const range = dateValue;\n            if (range === null || range === void 0 ? void 0 : range.from) {\n                displayValue = range.to ? \"\".concat(displayDate(range.from), \" - \").concat(displayDate(range.to)) : displayDate(range.from);\n            } else {\n                const currentFormatted = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(new Date(), dateFormat);\n                displayValue = \"\".concat(currentFormatted, \" - \").concat(currentFormatted);\n            }\n        } else {\n            displayValue = dateValue instanceof Date ? displayDate(dateValue) : (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(new Date(), dateFormat);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                    asChild: true,\n                    id: \"date\",\n                    position: labelPosition,\n                    disabled: disabled,\n                    children: label\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                    lineNumber: 623,\n                    columnNumber: 21\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        id: \"date\",\n                        variant: \"outline\",\n                        disabled: disabled,\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-4 justify-start w-full\"),\n                        iconLeft: renderIcon(\"mr-[8.5px] w-5 h-5 text-neutral-400\"),\n                        ...buttonProps,\n                        type: \"button\",\n                        children: displayValue\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 632,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                    lineNumber: 631,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 621,\n            columnNumber: 13\n        }, undefined);\n    }\n    const renderButtonLabel = ()=>{\n        if (mode === \"range\") {\n            const range = dateValue;\n            if (range === null || range === void 0 ? void 0 : range.from) {\n                return range.to ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        formatDateWithTime(range.from),\n                        \" -\",\n                        \" \",\n                        formatDateWithTime(range.to)\n                    ]\n                }, void 0, true) : formatDateWithTime(range.from);\n            }\n            // Use consistent date format for placeholder\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-muted-foreground\",\n                children: actualPlaceholder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 664,\n                columnNumber: 17\n            }, undefined);\n        } else if (dateValue) {\n            return formatDateWithTime(dateValue);\n        }\n        // Use consistent date format for placeholder\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-muted-foreground\",\n            children: actualPlaceholder\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 673,\n            columnNumber: 13\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(wrapperClassName),\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                id: \"date\",\n                position: labelPosition,\n                disabled: disabled,\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 680,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.Popover, {\n                modal: modal,\n                open: open,\n                onOpenChange: (isOpen)=>{\n                    // When opening the popover, initialize pendingSelection with the current value\n                    if (isOpen && !pendingSelection && dateValue) {\n                        setPendingSelection(dateValue);\n                    }\n                    setOpen(isOpen);\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.PopoverTrigger, {\n                        asChild: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    id: \"date\",\n                                    variant: \"outline\",\n                                    disabled: disabled,\n                                    iconLeft: renderIcon(),\n                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-4 justify-start w-full\", clearable && \"pr-10\"),\n                                    ...buttonProps,\n                                    type: \"button\",\n                                    children: renderButtonLabel()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 696,\n                                    columnNumber: 25\n                                }, undefined),\n                                clearable && (dateValue instanceof Date || (dateValue === null || dateValue === void 0 ? void 0 : dateValue.from)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"button\",\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    className: \"absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0\",\n                                    onClick: handleClear,\n                                    \"aria-label\": \"Clear date\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        size: 16,\n                                        className: \"text-neutral-400 hover:text-background0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 719,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 712,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 695,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 694,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.PopoverContent, {\n                        className: \"w-auto p-0\",\n                        align: \"start\",\n                        children: mode === \"range\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__.Calendar, {\n                                    autoFocus: true,\n                                    mode: \"range\",\n                                    month: (confirmSelection && pendingSelection ? pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from : dateValue === null || dateValue === void 0 ? void 0 : dateValue.from) || new Date(),\n                                    // allowFutureYears={allowFutureYears}\n                                    // showYearPicker={showYearPicker}\n                                    captionLayout: \"dropdown\",\n                                    selected: confirmSelection && pendingSelection ? pendingSelection : dateValue,\n                                    onSelect: (value)=>{\n                                        // Ensure we maintain the date format when a date is unselected\n                                        handleValueChange(value);\n                                    },\n                                    numberOfMonths: numberOfMonths,\n                                    showWeekNumber: showWeekNumbers,\n                                    disabled: validation ? (date)=>!validateDate(date) : undefined\n                                }, \"range\", false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 730,\n                                    columnNumber: 29\n                                }, undefined),\n                                shouldIncludeTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-t border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_time_picker__WEBPACK_IMPORTED_MODULE_5__.TimePicker, {\n                                            value: getTimePickerValue(time, pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from, setTime),\n                                            toValue: getTimePickerValue(toTime, pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.to, setToTime),\n                                            onChange: handleTimeChange,\n                                            onToChange: (date)=>handleTimeChange(date, true),\n                                            // Always enable time picker regardless of whether a date is selected\n                                            disabled: false,\n                                            className: \"w-full\",\n                                            mode: timeMode,\n                                            label: timeMode === \"range\" ? \"Time Range\" : undefined\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 763,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 762,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 761,\n                                    columnNumber: 33\n                                }, undefined),\n                                confirmSelection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                                            className: \"my-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 795,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-3 flex gap-3 justify-end\"),\n                                            children: [\n                                                clearable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-fit\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: handleClearInPopover,\n                                                        iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                                        className: \"w-fit\",\n                                                        \"aria-label\": \"Clear date range\",\n                                                        children: \"Clear\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                        lineNumber: 802,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 801,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: handleConfirm,\n                                                    disabled: !pendingSelection,\n                                                    iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                                    children: confirmButtonText\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 814,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 796,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 729,\n                            columnNumber: 25\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__.Calendar, {\n                                    autoFocus: true,\n                                    mode: \"single\",\n                                    month: (confirmSelection && pendingSelection ? pendingSelection : dateValue) || new Date(),\n                                    selected: confirmSelection && pendingSelection ? pendingSelection : dateValue,\n                                    onSelect: (value)=>{\n                                        // Ensure we maintain the date format when a date is unselected\n                                        handleValueChange(value);\n                                    },\n                                    captionLayout: \"dropdown\",\n                                    numberOfMonths: numberOfMonths,\n                                    showWeekNumber: showWeekNumbers,\n                                    disabled: validation ? (date)=>!validateDate(date) : undefined\n                                }, \"single\", false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 826,\n                                    columnNumber: 29\n                                }, undefined),\n                                shouldIncludeTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-t border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_time_picker__WEBPACK_IMPORTED_MODULE_5__.TimePicker, {\n                                            value: getTimePickerValue(time, pendingSelection instanceof Date ? pendingSelection : undefined, setTime),\n                                            toValue: getTimePickerValue(toTime, undefined, setToTime),\n                                            onChange: handleTimeChange,\n                                            onToChange: (date)=>handleTimeChange(date, true),\n                                            // Always enable time picker regardless of whether a date is selected\n                                            disabled: false,\n                                            className: \"w-full\",\n                                            mode: timeMode,\n                                            label: timeMode === \"range\" ? \"Time Range\" : undefined\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 856,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 855,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                    lineNumber: 854,\n                                    columnNumber: 33\n                                }, undefined),\n                                confirmSelection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                                            className: \"my-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 888,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-3 flex gap-3 justify-end\"),\n                                            children: [\n                                                clearable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-fit\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: handleClearInPopover,\n                                                        iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                                        \"aria-label\": \"Clear date\",\n                                                        children: \"Clear\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                        lineNumber: 895,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 894,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: handleConfirm,\n                                                    disabled: !pendingSelection,\n                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(clearable ? \"\" : \"w-full\"),\n                                                    children: confirmButtonText\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                                    lineNumber: 906,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 889,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 825,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 727,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 684,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n        lineNumber: 678,\n        columnNumber: 9\n    }, undefined);\n};\n_s(DatePicker, \"EPGvag/dmevMVfeSbcbdVrI0y2w=\");\n_c = DatePicker;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DatePicker);\nvar _c;\n$RefreshReg$(_c, \"DatePicker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DateRange.tsx\n"));

/***/ })

});